{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v8\",\n  key: \"1q4o3n\"\n}], [\"path\", {\n  d: \"m4.93 10.93 1.41 1.41\",\n  key: \"2a7f42\"\n}], [\"path\", {\n  d: \"M2 18h2\",\n  key: \"j10viu\"\n}], [\"path\", {\n  d: \"M20 18h2\",\n  key: \"wocana\"\n}], [\"path\", {\n  d: \"m19.07 10.93-1.41 1.41\",\n  key: \"15zs5n\"\n}], [\"path\", {\n  d: \"M22 22H2\",\n  key: \"19qnx5\"\n}], [\"path\", {\n  d: \"m8 6 4-4 4 4\",\n  key: \"ybng9g\"\n}], [\"path\", {\n  d: \"M16 18a4 4 0 0 0-8 0\",\n  key: \"1lzouq\"\n}]];\nconst Sunrise = createLucideIcon(\"sunrise\", __iconNode);\nexport { __iconNode, Sunrise as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Sunrise", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\lucide-react\\src\\icons\\sunrise.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2v8', key: '1q4o3n' }],\n  ['path', { d: 'm4.93 10.93 1.41 1.41', key: '2a7f42' }],\n  ['path', { d: 'M2 18h2', key: 'j10viu' }],\n  ['path', { d: 'M20 18h2', key: 'wocana' }],\n  ['path', { d: 'm19.07 10.93-1.41 1.41', key: '15zs5n' }],\n  ['path', { d: 'M22 22H2', key: '19qnx5' }],\n  ['path', { d: 'm8 6 4-4 4 4', key: 'ybng9g' }],\n  ['path', { d: 'M16 18a4 4 0 0 0-8 0', key: '1lzouq' }],\n];\n\n/**\n * @component @name Sunrise\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnY4IiAvPgogIDxwYXRoIGQ9Im00LjkzIDEwLjkzIDEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJNMiAxOGgyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxOGgyIiAvPgogIDxwYXRoIGQ9Im0xOS4wNyAxMC45My0xLjQxIDEuNDEiIC8+CiAgPHBhdGggZD0iTTIyIDIySDIiIC8+CiAgPHBhdGggZD0ibTggNiA0LTQgNCA0IiAvPgogIDxwYXRoIGQ9Ik0xNiAxOGE0IDQgMCAwIDAtOCAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/sunrise\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sunrise = createLucideIcon('sunrise', __iconNode);\n\nexport default Sunrise;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACvD;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}