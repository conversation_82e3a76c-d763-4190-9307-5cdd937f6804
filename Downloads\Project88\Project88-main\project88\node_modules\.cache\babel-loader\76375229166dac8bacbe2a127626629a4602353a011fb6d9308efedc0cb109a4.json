{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link isSameDay} function options.\n */\n\n/**\n * @name isSameDay\n * @category Day Helpers\n * @summary Are the given dates in the same day (and year and month)?\n *\n * @description\n * Are the given dates in the same day (and year and month)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same day (and year and month)\n *\n * @example\n * // Are 4 September 06:00:00 and 4 September 18:00:00 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 18, 0))\n * //=> true\n *\n * @example\n * // Are 4 September and 4 October in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2014, 9, 4))\n * //=> false\n *\n * @example\n * // Are 4 September, 2014 and 4 September, 2015 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2015, 8, 4))\n * //=> false\n */\nexport function isSameDay(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return +startOfDay(dateLeft_) === +startOfDay(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameDay;", "map": {"version": 3, "names": ["normalizeDates", "startOfDay", "isSameDay", "laterDate", "earlierDate", "options", "dateLeft_", "dateRight_", "in"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/isSameDay.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link isSameDay} function options.\n */\n\n/**\n * @name isSameDay\n * @category Day Helpers\n * @summary Are the given dates in the same day (and year and month)?\n *\n * @description\n * Are the given dates in the same day (and year and month)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same day (and year and month)\n *\n * @example\n * // Are 4 September 06:00:00 and 4 September 18:00:00 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 18, 0))\n * //=> true\n *\n * @example\n * // Are 4 September and 4 October in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2014, 9, 4))\n * //=> false\n *\n * @example\n * // Are 4 September, 2014 and 4 September, 2015 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2015, 8, 4))\n * //=> false\n */\nexport function isSameDay(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfDay(dateLeft_) === +startOfDay(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameDay;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACzD,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,GAAGP,cAAc,CAC5CK,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,OAAO,CAACH,UAAU,CAACK,SAAS,CAAC,KAAK,CAACL,UAAU,CAACM,UAAU,CAAC;AAC3D;;AAEA;AACA,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}