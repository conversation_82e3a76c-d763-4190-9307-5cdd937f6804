{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfQuarter} function options.\n */\n\n/**\n * @name startOfQuarter\n * @category Quarter Helpers\n * @summary Return the start of a year quarter for the given date.\n *\n * @description\n * Return the start of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a quarter\n *\n * @example\n * // The start of a quarter for 2 September 2014 11:55:00:\n * const result = startOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Jul 01 2014 00:00:00\n */\nexport function startOfQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - currentMonth % 3;\n  _date.setMonth(month, 1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfQuarter;", "map": {"version": 3, "names": ["toDate", "startOfQuarter", "date", "options", "_date", "in", "currentMonth", "getMonth", "month", "setMonth", "setHours"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/startOfQuarter.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfQuarter} function options.\n */\n\n/**\n * @name startOfQuarter\n * @category Quarter Helpers\n * @summary Return the start of a year quarter for the given date.\n *\n * @description\n * Return the start of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a quarter\n *\n * @example\n * // The start of a quarter for 2 September 2014 11:55:00:\n * const result = startOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Jul 01 2014 00:00:00\n */\nexport function startOfQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - (currentMonth % 3);\n  _date.setMonth(month, 1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfQuarter;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC5C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,YAAY,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;EACrC,MAAMC,KAAK,GAAGF,YAAY,GAAIA,YAAY,GAAG,CAAE;EAC/CF,KAAK,CAACK,QAAQ,CAACD,KAAK,EAAE,CAAC,CAAC;EACxBJ,KAAK,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAON,KAAK;AACd;;AAEA;AACA,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}