{"ast": null, "code": "import { addWeeks } from \"./addWeeks.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\n\n/**\n * The {@link getISOWeeksInYear} function options.\n */\n\n/**\n * @name getISOWeeksInYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * @description\n * Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of ISO weeks in a year\n *\n * @example\n * // How many weeks are in ISO week-numbering year 2015?\n * const result = getISOWeeksInYear(new Date(2015, 1, 11))\n * //=> 53\n */\nexport function getISOWeeksInYear(date, options) {\n  const thisYear = startOfISOWeekYear(date, options);\n  const nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));\n  const diff = +nextYear - +thisYear;\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default getISOWeeksInYear;", "map": {"version": 3, "names": ["addWeeks", "millisecondsInWeek", "startOfISOWeekYear", "getISOWeeksInYear", "date", "options", "thisYear", "nextYear", "diff", "Math", "round"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/getISOWeeksInYear.js"], "sourcesContent": ["import { addWeeks } from \"./addWeeks.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\n\n/**\n * The {@link getISOWeeksInYear} function options.\n */\n\n/**\n * @name getISOWeeksInYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * @description\n * Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of ISO weeks in a year\n *\n * @example\n * // How many weeks are in ISO week-numbering year 2015?\n * const result = getISOWeeksInYear(new Date(2015, 1, 11))\n * //=> 53\n */\nexport function getISOWeeksInYear(date, options) {\n  const thisYear = startOfISOWeekYear(date, options);\n  const nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));\n  const diff = +nextYear - +thisYear;\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default getISOWeeksInYear;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,kBAAkB,QAAQ,yBAAyB;;AAE5D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC/C,MAAMC,QAAQ,GAAGJ,kBAAkB,CAACE,IAAI,EAAEC,OAAO,CAAC;EAClD,MAAME,QAAQ,GAAGL,kBAAkB,CAACF,QAAQ,CAACM,QAAQ,EAAE,EAAE,CAAC,CAAC;EAC3D,MAAME,IAAI,GAAG,CAACD,QAAQ,GAAG,CAACD,QAAQ;;EAElC;EACA;EACA;EACA,OAAOG,IAAI,CAACC,KAAK,CAACF,IAAI,GAAGP,kBAAkB,CAAC;AAC9C;;AAEA;AACA,eAAeE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}