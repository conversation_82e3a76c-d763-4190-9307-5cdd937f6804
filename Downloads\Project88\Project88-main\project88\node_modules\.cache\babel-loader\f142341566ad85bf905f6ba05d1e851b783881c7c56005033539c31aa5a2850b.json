{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfMinute} function options.\n */\n\n/**\n * @name endOfMinute\n * @category Minute Helpers\n * @summary Return the end of a minute for the given date.\n *\n * @description\n * Return the end of a minute for the given date.\n * The result will be in the local timezone or the provided context.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a minute\n *\n * @example\n * // The end of a minute for 1 December 2014 22:15:45.400:\n * const result = endOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:59.999\n */\nexport function endOfMinute(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setSeconds(59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfMinute;", "map": {"version": 3, "names": ["toDate", "endOfMinute", "date", "options", "_date", "in", "setSeconds"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/endOfMinute.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfMinute} function options.\n */\n\n/**\n * @name endOfMinute\n * @category Minute Helpers\n * @summary Return the end of a minute for the given date.\n *\n * @description\n * Return the end of a minute for the given date.\n * The result will be in the local timezone or the provided context.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a minute\n *\n * @example\n * // The end of a minute for 1 December 2014 22:15:45.400:\n * const result = endOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:59.999\n */\nexport function endOfMinute(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setSeconds(59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfMinute;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACE,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC;EACzB,OAAOF,KAAK;AACd;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}