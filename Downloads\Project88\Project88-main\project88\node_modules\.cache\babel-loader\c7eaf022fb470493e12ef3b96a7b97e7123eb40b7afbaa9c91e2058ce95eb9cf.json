{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachMonthOfInterval} function options.\n */\n\n/**\n * The {@link eachMonthOfInterval} function result type. It resolves the proper data type.\n */\n\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of months from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * const result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\nexport function eachMonthOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n  date.setDate(1);\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setMonth(date.getMonth() + step);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMonthOfInterval;", "map": {"version": 3, "names": ["normalizeInterval", "constructFrom", "eachMonthOfInterval", "interval", "options", "start", "end", "in", "reversed", "endTime", "date", "setHours", "setDate", "step", "dates", "push", "setMonth", "getMonth", "reverse"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/eachMonthOfInterval.js"], "sourcesContent": ["import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachMonthOfInterval} function options.\n */\n\n/**\n * The {@link eachMonthOfInterval} function result type. It resolves the proper data type.\n */\n\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of months from the month of the interval start to the month of the interval end\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * const result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\nexport function eachMonthOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  const date = reversed ? end : start;\n  date.setHours(0, 0, 0, 0);\n  date.setDate(1);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date.setMonth(date.getMonth() + step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMonthOfInterval;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACrD,MAAM;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAGN,iBAAiB,CAACI,OAAO,EAAEG,EAAE,EAAEJ,QAAQ,CAAC;EAE/D,IAAIK,QAAQ,GAAG,CAACH,KAAK,GAAG,CAACC,GAAG;EAC5B,MAAMG,OAAO,GAAGD,QAAQ,GAAG,CAACH,KAAK,GAAG,CAACC,GAAG;EACxC,MAAMI,IAAI,GAAGF,QAAQ,GAAGF,GAAG,GAAGD,KAAK;EACnCK,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzBD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC;EAEf,IAAIC,IAAI,GAAGT,OAAO,EAAES,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZL,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMM,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACJ,IAAI,IAAID,OAAO,EAAE;IACvBK,KAAK,CAACC,IAAI,CAACd,aAAa,CAACI,KAAK,EAAEK,IAAI,CAAC,CAAC;IACtCA,IAAI,CAACM,QAAQ,CAACN,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAGJ,IAAI,CAAC;EACvC;EAEA,OAAOL,QAAQ,GAAGM,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGJ,KAAK;AAC3C;;AAEA;AACA,eAAeZ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}