{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 22v-5l5-5 5 5-5 5z\",\n  key: \"1fh25c\"\n}], [\"path\", {\n  d: \"M9.5 14.5 16 8\",\n  key: \"1smz5x\"\n}], [\"path\", {\n  d: \"m17 2 5 5-.5.5a3.53 3.53 0 0 1-5 0s0 0 0 0a3.53 3.53 0 0 1 0-5L17 2\",\n  key: \"1q8uv5\"\n}]];\nconst Shovel = createLucideIcon(\"shovel\", __iconNode);\nexport { __iconNode, Shovel as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\lucide-react\\src\\icons\\shovel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M2 22v-5l5-5 5 5-5 5z', key: '1fh25c' }],\n  ['path', { d: 'M9.5 14.5 16 8', key: '1smz5x' }],\n  [\n    'path',\n    { d: 'm17 2 5 5-.5.5a3.53 3.53 0 0 1-5 0s0 0 0 0a3.53 3.53 0 0 1 0-5L17 2', key: '1q8uv5' },\n  ],\n];\n\n/**\n * @component @name Shovel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMnYtNWw1LTUgNSA1LTUgNXoiIC8+CiAgPHBhdGggZD0iTTkuNSAxNC41IDE2IDgiIC8+CiAgPHBhdGggZD0ibTE3IDIgNSA1LS41LjVhMy41MyAzLjUzIDAgMCAxLTUgMHMwIDAgMCAwYTMuNTMgMy41MyAwIDAgMSAwLTVMMTcgMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shovel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shovel = createLucideIcon('shovel', __iconNode);\n\nexport default Shovel;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CACE,QACA;EAAED,CAAA,EAAG,qEAAuE;EAAAC,GAAA,EAAK;AAAS,EAC5F,CACF;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}