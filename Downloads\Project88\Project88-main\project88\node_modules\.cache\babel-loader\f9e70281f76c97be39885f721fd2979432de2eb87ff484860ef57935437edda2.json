{"ast": null, "code": "import { get as e, set as t } from \"react-hook-form\";\nconst r = (t, r, o) => {\n    if (t && \"reportValidity\" in t) {\n      const s = e(o, r);\n      t.setCustomValidity(s && s.message || \"\"), t.reportValidity();\n    }\n  },\n  o = (e, t) => {\n    for (const o in t.fields) {\n      const s = t.fields[o];\n      s && s.ref && \"reportValidity\" in s.ref ? r(s.ref, o, e) : s && s.refs && s.refs.forEach(t => r(t, o, e));\n    }\n  },\n  s = (r, s) => {\n    s.shouldUseNativeValidation && o(r, s);\n    const n = {};\n    for (const o in r) {\n      const f = e(s.fields, o),\n        c = Object.assign(r[o] || {}, {\n          ref: f && f.ref\n        });\n      if (i(s.names || Object.keys(r), o)) {\n        const r = Object.assign({}, e(n, o));\n        t(r, \"root\", c), t(n, o, r);\n      } else t(n, o, c);\n    }\n    return n;\n  },\n  i = (e, t) => {\n    const r = n(t);\n    return e.some(e => n(e).match(`^${r}\\\\.\\\\d+`));\n  };\nfunction n(e) {\n  return e.replace(/\\]|\\[/g, \"\");\n}\nexport { s as toNestErrors, o as validateFieldsNatively };", "map": {"version": 3, "names": ["r", "t", "o", "s", "e", "setCustomValidity", "message", "reportValidity", "fields", "ref", "refs", "for<PERSON>ach", "shouldUseNativeValidation", "n", "f", "c", "Object", "assign", "i", "names", "keys", "some", "match", "replace", "toNestErrors", "validateFieldsNatively"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\@hookform\\resolvers\\src\\validateFieldsNatively.ts", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\@hookform\\resolvers\\src\\toNestErrors.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field && field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => {\n  const path = escapeBrackets(name);\n  return names.some((n) => escapeBrackets(n).match(`^${path}\\\\.\\\\d+`));\n};\n\n/**\n * Escapes special characters in a string to be used in a regex pattern.\n * it removes the brackets from the string to match the `set` method.\n *\n * @param input - The input string to escape.\n * @returns The escaped string.\n */\nfunction escapeBrackets(input: string): string {\n  return input.replace(/\\]|\\[/g, '');\n}\n"], "mappings": ";AASA,MAAMA,CAAA,GAAoBA,CACxBC,CAAA,EACAD,CAAA,EACAE,CAAA;IAEA,IAAID,CAAA,IAAO,oBAAoBA,CAAA,EAAK;MAClC,MAAME,CAAA,GAAQC,CAAA,CAAIF,CAAA,EAAQF,CAAA;MAC1BC,CAAA,CAAII,iBAAA,CAAmBF,CAAA,IAASA,CAAA,CAAMG,OAAA,IAAY,KAElDL,CAAA,CAAIM,cAAA,EACN;IAAA;EAAA;EAIWL,CAAA,GAAyBA,CACpCE,CAAA,EACAH,CAAA;IAEA,KAAK,MAAMC,CAAA,IAAaD,CAAA,CAAQO,MAAA,EAAQ;MACtC,MAAML,CAAA,GAAQF,CAAA,CAAQO,MAAA,CAAON,CAAA;MACzBC,CAAA,IAASA,CAAA,CAAMM,GAAA,IAAO,oBAAoBN,CAAA,CAAMM,GAAA,GAClDT,CAAA,CAAkBG,CAAA,CAAMM,GAAA,EAAKP,CAAA,EAAWE,CAAA,IAC/BD,CAAA,IAASA,CAAA,CAAMO,IAAA,IACxBP,CAAA,CAAMO,IAAA,CAAKC,OAAA,CAASV,CAAA,IAClBD,CAAA,CAAkBC,CAAA,EAAKC,CAAA,EAAWE,CAAA,EAGxC;IAAA;EAAA;ECzBWD,CAAA,GAAeA,CAC1BH,CAAA,EACAG,CAAA;IAEAA,CAAA,CAAQS,yBAAA,IAA6BV,CAAA,CAAuBF,CAAA,EAAQG,CAAA;IAEpE,MAAMU,CAAA,GAAc;IACpB,KAAK,MAAMX,CAAA,IAAQF,CAAA,EAAQ;MACzB,MAAMc,CAAA,GAAQV,CAAA,CAAID,CAAA,CAAQK,MAAA,EAAQN,CAAA;QAC5Ba,CAAA,GAAQC,MAAA,CAAOC,MAAA,CAAOjB,CAAA,CAAOE,CAAA,KAAS,IAAI;UAC9CO,GAAA,EAAKK,CAAA,IAASA,CAAA,CAAML;QAAA;MAGtB,IAAIS,CAAA,CAAmBf,CAAA,CAAQgB,KAAA,IAASH,MAAA,CAAOI,IAAA,CAAKpB,CAAA,GAASE,CAAA,GAAO;QAClE,MAAMF,CAAA,GAAmBgB,MAAA,CAAOC,MAAA,CAAO,IAAIb,CAAA,CAAIS,CAAA,EAAaX,CAAA;QAE5DD,CAAA,CAAID,CAAA,EAAkB,QAAQe,CAAA,GAC9Bd,CAAA,CAAIY,CAAA,EAAaX,CAAA,EAAMF,CAAA,CACzB;MAAA,OACEC,CAAA,CAAIY,CAAA,EAAaX,CAAA,EAAMa,CAAA,CAE3B;IAAA;IAEA,OAAOF,CAAA;EAAA;EAGHK,CAAA,GAAqBA,CACzBd,CAAA,EACAH,CAAA;IAEA,MAAMD,CAAA,GAAOa,CAAA,CAAeZ,CAAA;IAC5B,OAAOG,CAAA,CAAMiB,IAAA,CAAMjB,CAAA,IAAMS,CAAA,CAAeT,CAAA,EAAGkB,KAAA,CAAM,IAAItB,CAAA,WAAc;EAAA;AAUrE,SAASa,EAAeT,CAAA;EACtB,OAAOA,CAAA,CAAMmB,OAAA,CAAQ,UAAU,GACjC;AAAA;AAAA,SAAApB,CAAA,IAAAqB,YAAA,EAAAtB,CAAA,IAAAuB,sBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}