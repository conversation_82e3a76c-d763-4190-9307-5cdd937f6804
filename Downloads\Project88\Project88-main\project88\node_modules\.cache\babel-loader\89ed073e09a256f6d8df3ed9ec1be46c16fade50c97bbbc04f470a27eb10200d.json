{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarISOWeekYears } from \"./differenceInCalendarISOWeekYears.js\";\nimport { subISOWeekYears } from \"./subISOWeekYears.js\";\n\n/**\n * The {@link differenceInISOWeekYears} function options.\n */\n\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options\n *\n * @returns The number of full ISO week-numbering years\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * // => 1\n */\nexport function differenceInISOWeekYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const sign = compareAsc(laterDate_, earlierDate_);\n  const diff = Math.abs(differenceInCalendarISOWeekYears(laterDate_, earlierDate_, options));\n  const adjustedDate = subISOWeekYears(laterDate_, sign * diff, options);\n  const isLastISOWeekYearNotFull = Number(compareAsc(adjustedDate, earlierDate_) === -sign);\n  const result = sign * (diff - isLastISOWeekYearNotFull);\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInISOWeekYears;", "map": {"version": 3, "names": ["normalizeDates", "compareAsc", "differenceInCalendarISOWeekYears", "subISOWeekYears", "differenceInISOWeekYears", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "sign", "diff", "Math", "abs", "adjustedDate", "isLastISOWeekYearNotFull", "Number", "result"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/differenceInISOWeekYears.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarISOWeekYears } from \"./differenceInCalendarISOWeekYears.js\";\nimport { subISOWeekYears } from \"./subISOWeekYears.js\";\n\n/**\n * The {@link differenceInISOWeekYears} function options.\n */\n\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options\n *\n * @returns The number of full ISO week-numbering years\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * // => 1\n */\nexport function differenceInISOWeekYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const sign = compareAsc(laterDate_, earlierDate_);\n  const diff = Math.abs(\n    differenceInCalendarISOWeekYears(laterDate_, earlierDate_, options),\n  );\n\n  const adjustedDate = subISOWeekYears(laterDate_, sign * diff, options);\n\n  const isLastISOWeekYearNotFull = Number(\n    compareAsc(adjustedDate, earlierDate_) === -sign,\n  );\n  const result = sign * (diff - isLastISOWeekYearNotFull);\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInISOWeekYears;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,gCAAgC,QAAQ,uCAAuC;AACxF,SAASC,eAAe,QAAQ,sBAAsB;;AAEtD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACxE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGT,cAAc,CAC/CO,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EAED,MAAMK,IAAI,GAAGV,UAAU,CAACO,UAAU,EAAEC,YAAY,CAAC;EACjD,MAAMG,IAAI,GAAGC,IAAI,CAACC,GAAG,CACnBZ,gCAAgC,CAACM,UAAU,EAAEC,YAAY,EAAEF,OAAO,CACpE,CAAC;EAED,MAAMQ,YAAY,GAAGZ,eAAe,CAACK,UAAU,EAAEG,IAAI,GAAGC,IAAI,EAAEL,OAAO,CAAC;EAEtE,MAAMS,wBAAwB,GAAGC,MAAM,CACrChB,UAAU,CAACc,YAAY,EAAEN,YAAY,CAAC,KAAK,CAACE,IAC9C,CAAC;EACD,MAAMO,MAAM,GAAGP,IAAI,IAAIC,IAAI,GAAGI,wBAAwB,CAAC;;EAEvD;EACA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA,eAAed,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}