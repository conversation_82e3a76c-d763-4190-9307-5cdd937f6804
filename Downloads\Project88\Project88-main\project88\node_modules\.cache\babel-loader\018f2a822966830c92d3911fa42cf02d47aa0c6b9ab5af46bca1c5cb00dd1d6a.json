{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m13.5 6.5-3.148-3.148a1.205 1.205 0 0 0-1.704 0L6.352 5.648a1.205 1.205 0 0 0 0 1.704L9.5 10.5\",\n  key: \"dzhfyz\"\n}], [\"path\", {\n  d: \"M16.5 7.5 19 5\",\n  key: \"1ltcjm\"\n}], [\"path\", {\n  d: \"m17.5 10.5 3.148 3.148a1.205 1.205 0 0 1 0 1.704l-2.296 2.296a1.205 1.205 0 0 1-1.704 0L13.5 14.5\",\n  key: \"nfoymv\"\n}], [\"path\", {\n  d: \"M9 21a6 6 0 0 0-6-6\",\n  key: \"1iajcf\"\n}], [\"path\", {\n  d: \"M9.352 10.648a1.205 1.205 0 0 0 0 1.704l2.296 2.296a1.205 1.205 0 0 0 1.704 0l4.296-4.296a1.205 1.205 0 0 0 0-1.704l-2.296-2.296a1.205 1.205 0 0 0-1.704 0z\",\n  key: \"nv9zqy\"\n}]];\nconst Satellite = createLucideIcon(\"satellite\", __iconNode);\nexport { __iconNode, Satellite as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Satellite", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\lucide-react\\src\\icons\\satellite.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm13.5 6.5-3.148-3.148a1.205 1.205 0 0 0-1.704 0L6.352 5.648a1.205 1.205 0 0 0 0 1.704L9.5 10.5',\n      key: 'dzhfyz',\n    },\n  ],\n  ['path', { d: 'M16.5 7.5 19 5', key: '1ltcjm' }],\n  [\n    'path',\n    {\n      d: 'm17.5 10.5 3.148 3.148a1.205 1.205 0 0 1 0 1.704l-2.296 2.296a1.205 1.205 0 0 1-1.704 0L13.5 14.5',\n      key: 'nfoymv',\n    },\n  ],\n  ['path', { d: 'M9 21a6 6 0 0 0-6-6', key: '1iajcf' }],\n  [\n    'path',\n    {\n      d: 'M9.352 10.648a1.205 1.205 0 0 0 0 1.704l2.296 2.296a1.205 1.205 0 0 0 1.704 0l4.296-4.296a1.205 1.205 0 0 0 0-1.704l-2.296-2.296a1.205 1.205 0 0 0-1.704 0z',\n      key: 'nv9zqy',\n    },\n  ],\n];\n\n/**\n * @component @name Satellite\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTMuNSA2LjUtMy4xNDgtMy4xNDhhMS4yMDUgMS4yMDUgMCAwIDAtMS43MDQgMEw2LjM1MiA1LjY0OGExLjIwNSAxLjIwNSAwIDAgMCAwIDEuNzA0TDkuNSAxMC41IiAvPgogIDxwYXRoIGQ9Ik0xNi41IDcuNSAxOSA1IiAvPgogIDxwYXRoIGQ9Im0xNy41IDEwLjUgMy4xNDggMy4xNDhhMS4yMDUgMS4yMDUgMCAwIDEgMCAxLjcwNGwtMi4yOTYgMi4yOTZhMS4yMDUgMS4yMDUgMCAwIDEtMS43MDQgMEwxMy41IDE0LjUiIC8+CiAgPHBhdGggZD0iTTkgMjFhNiA2IDAgMCAwLTYtNiIgLz4KICA8cGF0aCBkPSJNOS4zNTIgMTAuNjQ4YTEuMjA1IDEuMjA1IDAgMCAwIDAgMS43MDRsMi4yOTYgMi4yOTZhMS4yMDUgMS4yMDUgMCAwIDAgMS43MDQgMGw0LjI5Ni00LjI5NmExLjIwNSAxLjIwNSAwIDAgMCAwLTEuNzA0bC0yLjI5Ni0yLjI5NmExLjIwNSAxLjIwNSAwIDAgMC0xLjcwNCAweiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/satellite\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Satellite = createLucideIcon('satellite', __iconNode);\n\nexport default Satellite;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACF;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}