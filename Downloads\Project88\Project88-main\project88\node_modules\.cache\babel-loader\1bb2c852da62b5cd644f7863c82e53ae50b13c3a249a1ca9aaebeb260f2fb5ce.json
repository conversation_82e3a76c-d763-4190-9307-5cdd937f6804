{"ast": null, "code": "import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextSaturday} function options.\n */\n\n/**\n * @name nextSaturday\n * @category Weekday Helpers\n * @summary When is the next Saturday?\n *\n * @description\n * When is the next Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Saturday\n *\n * @example\n * // When is the next Saturday after Mar, 22, 2020?\n * const result = nextSaturday(new Date(2020, 2, 22))\n * //=> Sat Mar 28 2020 00:00:00\n */\nexport function nextSaturday(date, options) {\n  return nextDay(date, 6, options);\n}\n\n// Fallback for modularized imports:\nexport default nextSaturday;", "map": {"version": 3, "names": ["nextDay", "nextSaturday", "date", "options"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/nextSaturday.js"], "sourcesContent": ["import { nextDay } from \"./nextDay.js\";\n\n/**\n * The {@link nextSaturday} function options.\n */\n\n/**\n * @name nextSaturday\n * @category Weekday Helpers\n * @summary When is the next Saturday?\n *\n * @description\n * When is the next Saturday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The next Saturday\n *\n * @example\n * // When is the next Saturday after Mar, 22, 2020?\n * const result = nextSaturday(new Date(2020, 2, 22))\n * //=> Sat Mar 28 2020 00:00:00\n */\nexport function nextSaturday(date, options) {\n  return nextDay(date, 6, options);\n}\n\n// Fallback for modularized imports:\nexport default nextSaturday;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC1C,OAAOH,OAAO,CAACE,IAAI,EAAE,CAAC,EAAEC,OAAO,CAAC;AAClC;;AAEA;AACA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}