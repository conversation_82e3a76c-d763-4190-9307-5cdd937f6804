{"ast": null, "code": "import { getISOWeek } from \"./getISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISOWeek} function options.\n */\n\n/**\n * @name setISOWeek\n * @category ISO Week Helpers\n * @summary Set the ISO week to the given date.\n *\n * @description\n * Set the ISO week to the given date, saving the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The `Date` type of the context function.\n *\n * @param date - The date to be changed\n * @param week - The ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the ISO week set\n *\n * @example\n * // Set the 53rd ISO week to 7 August 2004:\n * const result = setISOWeek(new Date(2004, 7, 7), 53)\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport function setISOWeek(date, week, options) {\n  const _date = toDate(date, options?.in);\n  const diff = getISOWeek(_date, options) - week;\n  _date.setDate(_date.getDate() - diff * 7);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setISOWeek;", "map": {"version": 3, "names": ["getISOWeek", "toDate", "setISOWeek", "date", "week", "options", "_date", "in", "diff", "setDate", "getDate"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/setISOWeek.js"], "sourcesContent": ["import { getISOWeek } from \"./getISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISOWeek} function options.\n */\n\n/**\n * @name setISOWeek\n * @category ISO Week Helpers\n * @summary Set the ISO week to the given date.\n *\n * @description\n * Set the ISO week to the given date, saving the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The `Date` type of the context function.\n *\n * @param date - The date to be changed\n * @param week - The ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the ISO week set\n *\n * @example\n * // Set the 53rd ISO week to 7 August 2004:\n * const result = setISOWeek(new Date(2004, 7, 7), 53)\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport function setISOWeek(date, week, options) {\n  const _date = toDate(date, options?.in);\n  const diff = getISOWeek(_date, options) - week;\n  _date.setDate(_date.getDate() - diff * 7);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setISOWeek;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAC9C,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGR,UAAU,CAACM,KAAK,EAAED,OAAO,CAAC,GAAGD,IAAI;EAC9CE,KAAK,CAACG,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGF,IAAI,GAAG,CAAC,CAAC;EACzC,OAAOF,KAAK;AACd;;AAEA;AACA,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}