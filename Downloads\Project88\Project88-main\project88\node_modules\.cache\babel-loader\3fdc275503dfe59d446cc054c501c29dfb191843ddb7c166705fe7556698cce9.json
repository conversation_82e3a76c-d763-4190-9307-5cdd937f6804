{"ast": null, "code": "import { constructFrom } from \"../constructFrom.js\";\nexport function normalizeDates(context, ...dates) {\n  const normalize = constructFrom.bind(null, context || dates.find(date => typeof date === \"object\"));\n  return dates.map(normalize);\n}", "map": {"version": 3, "names": ["constructFrom", "normalizeDates", "context", "dates", "normalize", "bind", "find", "date", "map"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/_lib/normalizeDates.js"], "sourcesContent": ["import { constructFrom } from \"../constructFrom.js\";\n\nexport function normalizeDates(context, ...dates) {\n  const normalize = constructFrom.bind(\n    null,\n    context || dates.find((date) => typeof date === \"object\"),\n  );\n  return dates.map(normalize);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AAEnD,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAE,GAAGC,KAAK,EAAE;EAChD,MAAMC,SAAS,GAAGJ,aAAa,CAACK,IAAI,CAClC,IAAI,EACJH,OAAO,IAAIC,KAAK,CAACG,IAAI,CAAEC,IAAI,IAAK,OAAOA,IAAI,KAAK,QAAQ,CAC1D,CAAC;EACD,OAAOJ,KAAK,CAACK,GAAG,CAACJ,SAAS,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}