{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getUnixTime\n * @category Timestamp Helpers\n * @summary Get the seconds timestamp of the given date.\n *\n * @description\n * Get the seconds timestamp of the given date.\n *\n * @param date - The given date\n *\n * @returns The timestamp\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05 CET:\n * const result = getUnixTime(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 1330512305\n */\nexport function getUnixTime(date) {\n  return Math.trunc(+toDate(date) / 1000);\n}\n\n// Fallback for modularized imports:\nexport default getUnixTime;", "map": {"version": 3, "names": ["toDate", "getUnixTime", "date", "Math", "trunc"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/getUnixTime.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name getUnixTime\n * @category Timestamp Helpers\n * @summary Get the seconds timestamp of the given date.\n *\n * @description\n * Get the seconds timestamp of the given date.\n *\n * @param date - The given date\n *\n * @returns The timestamp\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05 CET:\n * const result = getUnixTime(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 1330512305\n */\nexport function getUnixTime(date) {\n  return Math.trunc(+toDate(date) / 1000);\n}\n\n// Fallback for modularized imports:\nexport default getUnixTime;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAOC,IAAI,CAACC,KAAK,CAAC,CAACJ,MAAM,CAACE,IAAI,CAAC,GAAG,IAAI,CAAC;AACzC;;AAEA;AACA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}