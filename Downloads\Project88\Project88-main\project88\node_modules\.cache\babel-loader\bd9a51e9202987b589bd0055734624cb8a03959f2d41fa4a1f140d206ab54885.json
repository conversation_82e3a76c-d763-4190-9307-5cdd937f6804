{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nimport { dayPeriodEnumToHours } from \"../utils.js\";\nexport class AMPMMidnightParser extends Parser {\n  priority = 80;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n      case \"bbb\":\n        return match.dayPeriod(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"bbbbb\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n      case \"bbbb\":\n      default:\n        return match.dayPeriod(dateString, {\n          width: \"wide\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"abbreviated\",\n          context: \"formatting\"\n        }) || match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\"\n        });\n    }\n  }\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"a\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "dayPeriodEnumToHours", "AMPMMidnightParser", "priority", "parse", "dateString", "token", "match", "<PERSON><PERSON><PERSON><PERSON>", "width", "context", "set", "date", "_flags", "value", "setHours", "incompatibleTokens"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/parse/_lib/parsers/AMPMMidnightParser.js"], "sourcesContent": ["import { Parser } from \"../Parser.js\";\n\nimport { dayPeriodEnumToHours } from \"../utils.js\";\n\nexport class AMPMMidnightParser extends Parser {\n  priority = 80;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n      case \"bbb\":\n        return (\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      case \"bbbbb\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return (\n          match.dayPeriod(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,oBAAoB,QAAQ,aAAa;AAElD,OAAO,MAAMC,kBAAkB,SAASF,MAAM,CAAC;EAC7CG,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,KAAK;QACR,OACEC,KAAK,CAACC,SAAS,CAACH,UAAU,EAAE;UAC1BI,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE;QACX,CAAC,CAAC,IACFH,KAAK,CAACC,SAAS,CAACH,UAAU,EAAE;UAC1BI,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;MAGN,KAAK,OAAO;QACV,OAAOH,KAAK,CAACC,SAAS,CAACH,UAAU,EAAE;UACjCI,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,KAAK,MAAM;MACX;QACE,OACEH,KAAK,CAACC,SAAS,CAACH,UAAU,EAAE;UAC1BI,KAAK,EAAE,MAAM;UACbC,OAAO,EAAE;QACX,CAAC,CAAC,IACFH,KAAK,CAACC,SAAS,CAACH,UAAU,EAAE;UAC1BI,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE;QACX,CAAC,CAAC,IACFH,KAAK,CAACC,SAAS,CAACH,UAAU,EAAE;UAC1BI,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;IAER;EACF;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACvBF,IAAI,CAACG,QAAQ,CAACd,oBAAoB,CAACa,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnD,OAAOF,IAAI;EACb;EAEAI,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}