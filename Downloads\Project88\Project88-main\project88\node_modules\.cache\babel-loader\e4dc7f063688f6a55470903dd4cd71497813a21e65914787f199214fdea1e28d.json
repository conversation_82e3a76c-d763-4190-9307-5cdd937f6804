{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 17a4 4 0 0 1-8 0V5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2Z\",\n  key: \"1ldrpk\"\n}], [\"path\", {\n  d: \"M16.7 13H19a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H7\",\n  key: \"11i5po\"\n}], [\"path\", {\n  d: \"M 7 17h.01\",\n  key: \"1euzgo\"\n}], [\"path\", {\n  d: \"m11 8 2.3-2.3a2.4 2.4 0 0 1 3.404.004L18.6 7.6a2.4 2.4 0 0 1 .026 3.434L9.9 19.8\",\n  key: \"o2gii7\"\n}]];\nconst SwatchBook = createLucideIcon(\"swatch-book\", __iconNode);\nexport { __iconNode, SwatchBook as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SwatchBook", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\lucide-react\\src\\icons\\swatch-book.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M11 17a4 4 0 0 1-8 0V5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2Z', key: '1ldrpk' }],\n  ['path', { d: 'M16.7 13H19a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H7', key: '11i5po' }],\n  ['path', { d: 'M 7 17h.01', key: '1euzgo' }],\n  [\n    'path',\n    {\n      d: 'm11 8 2.3-2.3a2.4 2.4 0 0 1 3.404.004L18.6 7.6a2.4 2.4 0 0 1 .026 3.434L9.9 19.8',\n      key: 'o2gii7',\n    },\n  ],\n];\n\n/**\n * @component @name SwatchBook\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMTdhNCA0IDAgMCAxLTggMFY1YTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAyWiIgLz4KICA8cGF0aCBkPSJNMTYuNyAxM0gxOWEyIDIgMCAwIDEgMiAydjRhMiAyIDAgMCAxLTIgMkg3IiAvPgogIDxwYXRoIGQ9Ik0gNyAxN2guMDEiIC8+CiAgPHBhdGggZD0ibTExIDggMi4zLTIuM2EyLjQgMi40IDAgMCAxIDMuNDA0LjAwNEwxOC42IDcuNmEyLjQgMi40IDAgMCAxIC4wMjYgMy40MzRMOS45IDE5LjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/swatch-book\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SwatchBook = createLucideIcon('swatch-book', __iconNode);\n\nexport default SwatchBook;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uDAAyD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtF,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACF;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}