{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link parseJSON} function options.\n */\n\n/**\n * Converts a complete ISO date string in UTC time, the typical format for transmitting\n * a date in JSON, to a JavaScript `Date` instance.\n *\n * This is a minimal implementation for converting dates retrieved from a JSON API to\n * a `Date` instance which can be used with other functions in the `date-fns` library.\n * The following formats are supported:\n *\n * - `2000-03-15T05:20:10.123Z`: The output of `.toISOString()` and `JSON.stringify(new Date())`\n * - `2000-03-15T05:20:10Z`: Without milliseconds\n * - `2000-03-15T05:20:10+00:00`: With a zero offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+05:45`: With a positive or negative offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+0000`: With a zero offset without a colon\n * - `2000-03-15T05:20:10`: Without a trailing 'Z' symbol\n * - `2000-03-15T05:20:10.1234567`: Up to 7 digits in milliseconds field. Only first 3 are taken into account since JS does not allow fractional milliseconds\n * - `2000-03-15 05:20:10`: With a space instead of a 'T' separator for APIs returning a SQL date without reformatting\n *\n * For convenience and ease of use these other input types are also supported\n * via [toDate](https://date-fns.org/docs/toDate):\n *\n * - A `Date` instance will be cloned\n * - A `number` will be treated as a timestamp\n *\n * Any other input type or invalid date strings will return an `Invalid Date`.\n *\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dateStr - A fully formed ISO8601 date string to convert\n * @param options - An object with options\n *\n * @returns The parsed date in the local time zone\n */\nexport function parseJSON(dateStr, options) {\n  const parts = dateStr.match(/(\\d{4})-(\\d{2})-(\\d{2})[T ](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{0,7}))?(?:Z|(.)(\\d{2}):?(\\d{2})?)?/);\n  if (!parts) return toDate(NaN, options?.in);\n  return toDate(Date.UTC(+parts[1], +parts[2] - 1, +parts[3], +parts[4] - (+parts[9] || 0) * (parts[8] == \"-\" ? -1 : 1), +parts[5] - (+parts[10] || 0) * (parts[8] == \"-\" ? -1 : 1), +parts[6], +((parts[7] || \"0\") + \"00\").substring(0, 3)), options?.in);\n}\n\n// Fallback for modularized imports:\nexport default parseJSON;", "map": {"version": 3, "names": ["toDate", "parseJSON", "dateStr", "options", "parts", "match", "NaN", "in", "Date", "UTC", "substring"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/parseJSON.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link parseJSON} function options.\n */\n\n/**\n * Converts a complete ISO date string in UTC time, the typical format for transmitting\n * a date in JSON, to a JavaScript `Date` instance.\n *\n * This is a minimal implementation for converting dates retrieved from a JSON API to\n * a `Date` instance which can be used with other functions in the `date-fns` library.\n * The following formats are supported:\n *\n * - `2000-03-15T05:20:10.123Z`: The output of `.toISOString()` and `JSON.stringify(new Date())`\n * - `2000-03-15T05:20:10Z`: Without milliseconds\n * - `2000-03-15T05:20:10+00:00`: With a zero offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+05:45`: With a positive or negative offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+0000`: With a zero offset without a colon\n * - `2000-03-15T05:20:10`: Without a trailing 'Z' symbol\n * - `2000-03-15T05:20:10.1234567`: Up to 7 digits in milliseconds field. Only first 3 are taken into account since JS does not allow fractional milliseconds\n * - `2000-03-15 05:20:10`: With a space instead of a 'T' separator for APIs returning a SQL date without reformatting\n *\n * For convenience and ease of use these other input types are also supported\n * via [toDate](https://date-fns.org/docs/toDate):\n *\n * - A `Date` instance will be cloned\n * - A `number` will be treated as a timestamp\n *\n * Any other input type or invalid date strings will return an `Invalid Date`.\n *\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dateStr - A fully formed ISO8601 date string to convert\n * @param options - An object with options\n *\n * @returns The parsed date in the local time zone\n */\nexport function parseJSON(dateStr, options) {\n  const parts = dateStr.match(\n    /(\\d{4})-(\\d{2})-(\\d{2})[T ](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{0,7}))?(?:Z|(.)(\\d{2}):?(\\d{2})?)?/,\n  );\n\n  if (!parts) return toDate(NaN, options?.in);\n\n  return toDate(\n    Date.UTC(\n      +parts[1],\n      +parts[2] - 1,\n      +parts[3],\n      +parts[4] - (+parts[9] || 0) * (parts[8] == \"-\" ? -1 : 1),\n      +parts[5] - (+parts[10] || 0) * (parts[8] == \"-\" ? -1 : 1),\n      +parts[6],\n      +((parts[7] || \"0\") + \"00\").substring(0, 3),\n    ),\n    options?.in,\n  );\n}\n\n// Fallback for modularized imports:\nexport default parseJSON;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC1C,MAAMC,KAAK,GAAGF,OAAO,CAACG,KAAK,CACzB,+FACF,CAAC;EAED,IAAI,CAACD,KAAK,EAAE,OAAOJ,MAAM,CAACM,GAAG,EAAEH,OAAO,EAAEI,EAAE,CAAC;EAE3C,OAAOP,MAAM,CACXQ,IAAI,CAACC,GAAG,CACN,CAACL,KAAK,CAAC,CAAC,CAAC,EACT,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EACb,CAACA,KAAK,CAAC,CAAC,CAAC,EACT,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EACzD,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAACA,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAC1D,CAACA,KAAK,CAAC,CAAC,CAAC,EACT,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,EAAEM,SAAS,CAAC,CAAC,EAAE,CAAC,CAC5C,CAAC,EACDP,OAAO,EAAEI,EACX,CAAC;AACH;;AAEA;AACA,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}