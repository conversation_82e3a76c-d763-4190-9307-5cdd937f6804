{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 10V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l2-1.14\",\n  key: \"e7tb2h\"\n}], [\"path\", {\n  d: \"m7.5 4.27 9 5.15\",\n  key: \"1c824w\"\n}], [\"polyline\", {\n  points: \"3.29 7 12 12 20.71 7\",\n  key: \"ousv84\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"12\",\n  key: \"a4e8g8\"\n}], [\"circle\", {\n  cx: \"18.5\",\n  cy: \"15.5\",\n  r: \"2.5\",\n  key: \"b5zd12\"\n}], [\"path\", {\n  d: \"M20.27 17.27 22 19\",\n  key: \"1l4muz\"\n}]];\nconst PackageSearch = createLucideIcon(\"package-search\", __iconNode);\nexport { __iconNode, PackageSearch as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "points", "x1", "x2", "y1", "y2", "cx", "cy", "r", "PackageSearch", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\lucide-react\\src\\icons\\package-search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M21 10V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l2-1.14',\n      key: 'e7tb2h',\n    },\n  ],\n  ['path', { d: 'm7.5 4.27 9 5.15', key: '1c824w' }],\n  ['polyline', { points: '3.29 7 12 12 20.71 7', key: 'ousv84' }],\n  ['line', { x1: '12', x2: '12', y1: '22', y2: '12', key: 'a4e8g8' }],\n  ['circle', { cx: '18.5', cy: '15.5', r: '2.5', key: 'b5zd12' }],\n  ['path', { d: 'M20.27 17.27 22 19', key: '1l4muz' }],\n];\n\n/**\n * @component @name PackageSearch\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTBWOGEyIDIgMCAwIDAtMS0xLjczbC03LTRhMiAyIDAgMCAwLTIgMGwtNyA0QTIgMiAwIDAgMCAzIDh2OGEyIDIgMCAwIDAgMSAxLjczbDcgNGEyIDIgMCAwIDAgMiAwbDItMS4xNCIgLz4KICA8cGF0aCBkPSJtNy41IDQuMjcgOSA1LjE1IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjMuMjkgNyAxMiAxMiAyMC43MSA3IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMjIiIHkyPSIxMiIgLz4KICA8Y2lyY2xlIGN4PSIxOC41IiBjeT0iMTUuNSIgcj0iMi41IiAvPgogIDxwYXRoIGQ9Ik0yMC4yNyAxNy4yNyAyMiAxOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/package-search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PackageSearch = createLucideIcon('package-search', __iconNode);\n\nexport default PackageSearch;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,UAAY;EAAEC,MAAA,EAAQ,sBAAwB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAEE,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAL,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAU;EAAEM,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAOR,GAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,EACrD;AAaM,MAAAS,aAAA,GAAgBC,gBAAiB,mBAAkBZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}