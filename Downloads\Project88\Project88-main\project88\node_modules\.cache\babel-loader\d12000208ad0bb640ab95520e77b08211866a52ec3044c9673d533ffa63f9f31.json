{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfDecade} function options.\n */\n\n/**\n * @name lastDayOfDecade\n * @category Decade Helpers\n * @summary Return the last day of a decade for the given date.\n *\n * @description\n * Return the last day of a decade for the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type; inferred from arguments or specified by context.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The last day of a decade\n *\n * @example\n * // The last day of a decade for 21 December 2012 21:12:00:\n * const result = lastDayOfDecade(new Date(2012, 11, 21, 21, 12, 00))\n * //=> Wed Dec 31 2019 00:00:00\n */\nexport function lastDayOfDecade(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = 9 + Math.floor(year / 10) * 10;\n  _date.setFullYear(decade + 1, 0, 0);\n  _date.setHours(0, 0, 0, 0);\n  return toDate(_date, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfDecade;", "map": {"version": 3, "names": ["toDate", "lastDayOfDecade", "date", "options", "_date", "in", "year", "getFullYear", "decade", "Math", "floor", "setFullYear", "setHours"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/lastDayOfDecade.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfDecade} function options.\n */\n\n/**\n * @name lastDayOfDecade\n * @category Decade Helpers\n * @summary Return the last day of a decade for the given date.\n *\n * @description\n * Return the last day of a decade for the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type; inferred from arguments or specified by context.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The last day of a decade\n *\n * @example\n * // The last day of a decade for 21 December 2012 21:12:00:\n * const result = lastDayOfDecade(new Date(2012, 11, 21, 21, 12, 00))\n * //=> Wed Dec 31 2019 00:00:00\n */\nexport function lastDayOfDecade(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = 9 + Math.floor(year / 10) * 10;\n  _date.setFullYear(decade + 1, 0, 0);\n  _date.setHours(0, 0, 0, 0);\n  return toDate(_date, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfDecade;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC7C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChC,MAAMC,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE;EAC7CF,KAAK,CAACO,WAAW,CAACH,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnCJ,KAAK,CAACQ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAOZ,MAAM,CAACI,KAAK,EAAED,OAAO,EAAEE,EAAE,CAAC;AACnC;;AAEA;AACA,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}