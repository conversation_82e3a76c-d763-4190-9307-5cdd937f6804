[{"C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\App.js": "3", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\Test.js": "5", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\LoginPage.js": "6", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\FogotPassword.js": "7", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\VerifyPage.js": "8", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\ChangePassword.js": "9", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\HomePage.js": "10", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\Transfer.js": "11", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\register\\Register.js": "12", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\admin\\EmployeeList.js": "13", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Header.js": "14", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Footer.js": "15", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\admin\\AdminContent.js": "16", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\Profile.js": "17", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\UserContent.js": "18", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\employee\\EmployeeContent.js": "19", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\userSlice.js": "20", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\transactionSlice.js": "21", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\registerSlice.js": "22", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\employeeSlice.js": "23", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\depositSlice.js": "24", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Form.js": "25", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\TransferForm.js": "26", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\EditUserModal.js": "27", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Search.js": "28", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Table.js": "29", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Pagination.js": "30", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\TopUp.js": "31", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\TranferUserBalance.js": "32", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\ValidationLogin.js": "33", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\MyDatePicker.js": "34", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\ValidationChangePassword.js": "35", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\ProfileContent.js": "36", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\Redeem.js": "37", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\Validation.js": "38", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\Deposit.js": "39", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\Bills.js": "40", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\Transaction.js": "41", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\SavingTotal.js": "42", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\AuthAPI.js": "43", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\UsersList.js": "44", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\TransactionModal.js": "45", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\ProfileService.js": "46", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\admin\\CreateEmployee.js": "47", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\register\\RegisterService.js": "48", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\AdminAPI.js": "49", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\UserAPIv2.js": "50", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\UserService.js": "51", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\TransactionService.js": "52", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\utils\\auth.js": "53", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\employee\\EmployeeService.js": "54", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\DepositService.js": "55", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\EditUserValidatiion.js": "56", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\UserApi.js": "57", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\DepositAPI.js": "58", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\PaymentModal.js": "59", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\axiosClient.js": "60", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\CreateEmployeeValidation.js": "61", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\TransactionAPI.js": "62", "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\EmployeeAPI.js": "63"}, {"size": 611, "mtime": 1751338629795, "results": "64", "hashOfConfig": "65"}, {"size": 362, "mtime": 1751338629795, "results": "66", "hashOfConfig": "65"}, {"size": 2385, "mtime": 1751338629717, "results": "67", "hashOfConfig": "65"}, {"size": 738, "mtime": 1751338629795, "results": "68", "hashOfConfig": "65"}, {"size": 1001, "mtime": 1751338629748, "results": "69", "hashOfConfig": "65"}, {"size": 1772, "mtime": 1751338629764, "results": "70", "hashOfConfig": "65"}, {"size": 2186, "mtime": 1751338629764, "results": "71", "hashOfConfig": "65"}, {"size": 1255, "mtime": 1751338629764, "results": "72", "hashOfConfig": "65"}, {"size": 1496, "mtime": 1751338629764, "results": "73", "hashOfConfig": "65"}, {"size": 1065, "mtime": 1751338629764, "results": "74", "hashOfConfig": "65"}, {"size": 3515, "mtime": 1751338629779, "results": "75", "hashOfConfig": "65"}, {"size": 1750, "mtime": 1751338629779, "results": "76", "hashOfConfig": "65"}, {"size": 1236, "mtime": 1751338629764, "results": "77", "hashOfConfig": "65"}, {"size": 5108, "mtime": 1751338629733, "results": "78", "hashOfConfig": "65"}, {"size": 147, "mtime": 1751338629733, "results": "79", "hashOfConfig": "65"}, {"size": 4525, "mtime": 1751338629748, "results": "80", "hashOfConfig": "65"}, {"size": 348, "mtime": 1751338629779, "results": "81", "hashOfConfig": "65"}, {"size": 8597, "mtime": 1751338629779, "results": "82", "hashOfConfig": "65"}, {"size": 6239, "mtime": 1751338629764, "results": "83", "hashOfConfig": "65"}, {"size": 5420, "mtime": 1751338629795, "results": "84", "hashOfConfig": "65"}, {"size": 4962, "mtime": 1751338629795, "results": "85", "hashOfConfig": "65"}, {"size": 1433, "mtime": 1751338629795, "results": "86", "hashOfConfig": "65"}, {"size": 3261, "mtime": 1751338629795, "results": "87", "hashOfConfig": "65"}, {"size": 1477, "mtime": 1751338629795, "results": "88", "hashOfConfig": "65"}, {"size": 4529, "mtime": 1751338629733, "results": "89", "hashOfConfig": "65"}, {"size": 2114, "mtime": 1751338629779, "results": "90", "hashOfConfig": "65"}, {"size": 3009, "mtime": 1751338629733, "results": "91", "hashOfConfig": "65"}, {"size": 1825, "mtime": 1751338629748, "results": "92", "hashOfConfig": "65"}, {"size": 6594, "mtime": 1751338629748, "results": "93", "hashOfConfig": "65"}, {"size": 1418, "mtime": 1751338629748, "results": "94", "hashOfConfig": "65"}, {"size": 2054, "mtime": 1751338629764, "results": "95", "hashOfConfig": "65"}, {"size": 905, "mtime": 1751338629748, "results": "96", "hashOfConfig": "65"}, {"size": 236, "mtime": 1751338629811, "results": "97", "hashOfConfig": "65"}, {"size": 1161, "mtime": 1751338629733, "results": "98", "hashOfConfig": "65"}, {"size": 404, "mtime": 1751338629811, "results": "99", "hashOfConfig": "65"}, {"size": 6942, "mtime": 1751338629779, "results": "100", "hashOfConfig": "65"}, {"size": 13162, "mtime": 1751338629764, "results": "101", "hashOfConfig": "65"}, {"size": 2183, "mtime": 1751338629811, "results": "102", "hashOfConfig": "65"}, {"size": 6980, "mtime": 1751338629779, "results": "103", "hashOfConfig": "65"}, {"size": 1519, "mtime": 1751338629764, "results": "104", "hashOfConfig": "65"}, {"size": 1175, "mtime": 1751338629779, "results": "105", "hashOfConfig": "65"}, {"size": 3643, "mtime": 1751338629779, "results": "106", "hashOfConfig": "65"}, {"size": 564, "mtime": 1751338629764, "results": "107", "hashOfConfig": "65"}, {"size": 1200, "mtime": 1751338629795, "results": "108", "hashOfConfig": "65"}, {"size": 3835, "mtime": 1751338629748, "results": "109", "hashOfConfig": "65"}, {"size": 182, "mtime": 1751338629779, "results": "110", "hashOfConfig": "65"}, {"size": 1446, "mtime": 1751338629748, "results": "111", "hashOfConfig": "65"}, {"size": 648, "mtime": 1751338629779, "results": "112", "hashOfConfig": "65"}, {"size": 253, "mtime": 1751338629717, "results": "113", "hashOfConfig": "65"}, {"size": 686, "mtime": 1751338629717, "results": "114", "hashOfConfig": "65"}, {"size": 474, "mtime": 1751338629779, "results": "115", "hashOfConfig": "65"}, {"size": 494, "mtime": 1751338629779, "results": "116", "hashOfConfig": "65"}, {"size": 489, "mtime": 1751338629811, "results": "117", "hashOfConfig": "65"}, {"size": 520, "mtime": 1751338629764, "results": "118", "hashOfConfig": "65"}, {"size": 404, "mtime": 1751338629779, "results": "119", "hashOfConfig": "65"}, {"size": 1011, "mtime": 1751338629811, "results": "120", "hashOfConfig": "65"}, {"size": 1385, "mtime": 1751338629733, "results": "121", "hashOfConfig": "65"}, {"size": 525, "mtime": 1751338629717, "results": "122", "hashOfConfig": "65"}, {"size": 2467, "mtime": 1751338629748, "results": "123", "hashOfConfig": "65"}, {"size": 1119, "mtime": 1751338629733, "results": "124", "hashOfConfig": "65"}, {"size": 1857, "mtime": 1751338629811, "results": "125", "hashOfConfig": "65"}, {"size": 492, "mtime": 1751338629717, "results": "126", "hashOfConfig": "65"}, {"size": 501, "mtime": 1751338629717, "results": "127", "hashOfConfig": "65"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "e39bke", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\Test.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\FogotPassword.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\VerifyPage.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\auth\\ChangePassword.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\HomePage.js", ["317", "318", "319"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\Transfer.js", ["320", "321"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\register\\Register.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\admin\\EmployeeList.js", ["322", "323", "324", "325", "326", "327", "328", "329", "330", "331"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\admin\\AdminContent.js", ["332", "333", "334", "335", "336", "337", "338", "339"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\Profile.js", ["340", "341"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\UserContent.js", ["342", "343", "344", "345", "346", "347", "348", "349"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\employee\\EmployeeContent.js", ["350", "351", "352", "353", "354", "355", "356", "357", "358"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\userSlice.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\transactionSlice.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\registerSlice.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\employeeSlice.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\redux\\slices\\depositSlice.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Form.js", ["359"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\TransferForm.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\EditUserModal.js", ["360", "361", "362"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Search.js", ["363"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Table.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\Pagination.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\TopUp.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\TranferUserBalance.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\ValidationLogin.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\MyDatePicker.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\ValidationChangePassword.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\ProfileContent.js", ["364", "365", "366"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\Redeem.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\Validation.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\Deposit.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\home\\Bills.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\Transaction.js", ["367", "368", "369", "370", "371"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\SavingTotal.js", ["372", "373", "374"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\AuthAPI.js", ["375"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\UsersList.js", ["376", "377", "378", "379", "380"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\TransactionModal.js", ["381", "382", "383", "384"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\ProfileService.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\admin\\CreateEmployee.js", ["385"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\register\\RegisterService.js", ["386"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\AdminAPI.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\UserAPIv2.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\UserService.js", ["387"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\TransactionService.js", ["388"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\utils\\auth.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\employee\\EmployeeService.js", ["389"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\features\\user\\DepositService.js", ["390"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\EditUserValidatiion.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\UserApi.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\DepositAPI.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\components\\PaymentModal.js", ["391"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\axiosClient.js", ["392", "393"], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\validation\\CreateEmployeeValidation.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\TransactionAPI.js", [], [], "C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\src\\api\\EmployeeAPI.js", [], [], {"ruleId": "394", "severity": 1, "message": "395", "line": 2, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 14}, {"ruleId": "394", "severity": 1, "message": "398", "line": 3, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 3, "endColumn": 14}, {"ruleId": "394", "severity": 1, "message": "399", "line": 19, "column": 11, "nodeType": "396", "messageId": "397", "endLine": 19, "endColumn": 22}, {"ruleId": "394", "severity": 1, "message": "400", "line": 48, "column": 13, "nodeType": "396", "messageId": "397", "endLine": 48, "endColumn": 21}, {"ruleId": "401", "severity": 1, "message": "402", "line": 110, "column": 6, "nodeType": "403", "endLine": 110, "endColumn": 8, "suggestions": "404"}, {"ruleId": "394", "severity": 1, "message": "405", "line": 1, "column": 17, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 25}, {"ruleId": "394", "severity": 1, "message": "406", "line": 2, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 14}, {"ruleId": "394", "severity": 1, "message": "407", "line": 2, "column": 16, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 22}, {"ruleId": "394", "severity": 1, "message": "408", "line": 2, "column": 24, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 30}, {"ruleId": "394", "severity": 1, "message": "409", "line": 2, "column": 32, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 36}, {"ruleId": "394", "severity": 1, "message": "410", "line": 6, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 6, "endColumn": 16}, {"ruleId": "394", "severity": 1, "message": "411", "line": 10, "column": 11, "nodeType": "396", "messageId": "397", "endLine": 10, "endColumn": 19}, {"ruleId": "394", "severity": 1, "message": "412", "line": 14, "column": 24, "nodeType": "396", "messageId": "397", "endLine": 14, "endColumn": 31}, {"ruleId": "394", "severity": 1, "message": "413", "line": 14, "column": 33, "nodeType": "396", "messageId": "397", "endLine": 14, "endColumn": 38}, {"ruleId": "401", "severity": 1, "message": "414", "line": 20, "column": 23, "nodeType": "415", "endLine": 20, "endColumn": 24}, {"ruleId": "394", "severity": 1, "message": "416", "line": 2, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 13}, {"ruleId": "394", "severity": 1, "message": "417", "line": 7, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 7, "endColumn": 21}, {"ruleId": "394", "severity": 1, "message": "418", "line": 22, "column": 12, "nodeType": "396", "messageId": "397", "endLine": 22, "endColumn": 24}, {"ruleId": "394", "severity": 1, "message": "419", "line": 22, "column": 26, "nodeType": "396", "messageId": "397", "endLine": 22, "endColumn": 41}, {"ruleId": "394", "severity": 1, "message": "420", "line": 23, "column": 12, "nodeType": "396", "messageId": "397", "endLine": 23, "endColumn": 20}, {"ruleId": "394", "severity": 1, "message": "421", "line": 23, "column": 22, "nodeType": "396", "messageId": "397", "endLine": 23, "endColumn": 33}, {"ruleId": "394", "severity": 1, "message": "422", "line": 27, "column": 13, "nodeType": "396", "messageId": "397", "endLine": 27, "endColumn": 26}, {"ruleId": "394", "severity": 1, "message": "423", "line": 27, "column": 40, "nodeType": "396", "messageId": "397", "endLine": 27, "endColumn": 51}, {"ruleId": "394", "severity": 1, "message": "395", "line": 2, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 14}, {"ruleId": "394", "severity": 1, "message": "398", "line": 3, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 3, "endColumn": 14}, {"ruleId": "394", "severity": 1, "message": "424", "line": 1, "column": 17, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 20}, {"ruleId": "394", "severity": 1, "message": "425", "line": 4, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 4, "endColumn": 15}, {"ruleId": "394", "severity": 1, "message": "426", "line": 5, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 5, "endColumn": 14}, {"ruleId": "394", "severity": 1, "message": "427", "line": 15, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 15, "endColumn": 21}, {"ruleId": "394", "severity": 1, "message": "423", "line": 57, "column": 23, "nodeType": "396", "messageId": "397", "endLine": 57, "endColumn": 34}, {"ruleId": "394", "severity": 1, "message": "400", "line": 108, "column": 13, "nodeType": "396", "messageId": "397", "endLine": 108, "endColumn": 21}, {"ruleId": "401", "severity": 1, "message": "428", "line": 150, "column": 6, "nodeType": "403", "endLine": 150, "endColumn": 15, "suggestions": "429"}, {"ruleId": "401", "severity": 1, "message": "430", "line": 156, "column": 6, "nodeType": "403", "endLine": 156, "endColumn": 34, "suggestions": "431"}, {"ruleId": "394", "severity": 1, "message": "432", "line": 15, "column": 12, "nodeType": "396", "messageId": "397", "endLine": 15, "endColumn": 18}, {"ruleId": "394", "severity": 1, "message": "433", "line": 15, "column": 20, "nodeType": "396", "messageId": "397", "endLine": 15, "endColumn": 29}, {"ruleId": "394", "severity": 1, "message": "434", "line": 20, "column": 12, "nodeType": "396", "messageId": "397", "endLine": 20, "endColumn": 29}, {"ruleId": "394", "severity": 1, "message": "435", "line": 20, "column": 31, "nodeType": "396", "messageId": "397", "endLine": 20, "endColumn": 51}, {"ruleId": "394", "severity": 1, "message": "436", "line": 21, "column": 12, "nodeType": "396", "messageId": "397", "endLine": 21, "endColumn": 28}, {"ruleId": "394", "severity": 1, "message": "437", "line": 21, "column": 30, "nodeType": "396", "messageId": "397", "endLine": 21, "endColumn": 49}, {"ruleId": "394", "severity": 1, "message": "423", "line": 31, "column": 25, "nodeType": "396", "messageId": "397", "endLine": 31, "endColumn": 36}, {"ruleId": "394", "severity": 1, "message": "412", "line": 33, "column": 33, "nodeType": "396", "messageId": "397", "endLine": 33, "endColumn": 40}, {"ruleId": "394", "severity": 1, "message": "413", "line": 33, "column": 42, "nodeType": "396", "messageId": "397", "endLine": 33, "endColumn": 47}, {"ruleId": "394", "severity": 1, "message": "438", "line": 1, "column": 17, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 26}, {"ruleId": "394", "severity": 1, "message": "405", "line": 1, "column": 17, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 25}, {"ruleId": "394", "severity": 1, "message": "439", "line": 4, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 4, "endColumn": 15}, {"ruleId": "394", "severity": 1, "message": "440", "line": 5, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 5, "endColumn": 18}, {"ruleId": "401", "severity": 1, "message": "441", "line": 29, "column": 8, "nodeType": "403", "endLine": 29, "endColumn": 17, "suggestions": "442"}, {"ruleId": "394", "severity": 1, "message": "443", "line": 4, "column": 18, "nodeType": "396", "messageId": "397", "endLine": 4, "endColumn": 21}, {"ruleId": "394", "severity": 1, "message": "444", "line": 17, "column": 12, "nodeType": "396", "messageId": "397", "endLine": 17, "endColumn": 20}, {"ruleId": "445", "severity": 1, "message": "446", "line": 70, "column": 33, "nodeType": "447", "endLine": 70, "endColumn": 147}, {"ruleId": "394", "severity": 1, "message": "443", "line": 5, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 5, "endColumn": 13}, {"ruleId": "394", "severity": 1, "message": "448", "line": 6, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 6, "endColumn": 17}, {"ruleId": "394", "severity": 1, "message": "412", "line": 14, "column": 27, "nodeType": "396", "messageId": "397", "endLine": 14, "endColumn": 34}, {"ruleId": "394", "severity": 1, "message": "413", "line": 14, "column": 36, "nodeType": "396", "messageId": "397", "endLine": 14, "endColumn": 41}, {"ruleId": "394", "severity": 1, "message": "449", "line": 15, "column": 18, "nodeType": "396", "messageId": "397", "endLine": 15, "endColumn": 25}, {"ruleId": "394", "severity": 1, "message": "450", "line": 9, "column": 13, "nodeType": "396", "messageId": "397", "endLine": 9, "endColumn": 21}, {"ruleId": "394", "severity": 1, "message": "412", "line": 9, "column": 23, "nodeType": "396", "messageId": "397", "endLine": 9, "endColumn": 30}, {"ruleId": "394", "severity": 1, "message": "413", "line": 9, "column": 32, "nodeType": "396", "messageId": "397", "endLine": 9, "endColumn": 37}, {"ruleId": "394", "severity": 1, "message": "451", "line": 1, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 22}, {"ruleId": "394", "severity": 1, "message": "424", "line": 1, "column": 17, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 20}, {"ruleId": "394", "severity": 1, "message": "449", "line": 9, "column": 18, "nodeType": "396", "messageId": "397", "endLine": 9, "endColumn": 25}, {"ruleId": "394", "severity": 1, "message": "412", "line": 20, "column": 20, "nodeType": "396", "messageId": "397", "endLine": 20, "endColumn": 27}, {"ruleId": "394", "severity": 1, "message": "413", "line": 20, "column": 29, "nodeType": "396", "messageId": "397", "endLine": 20, "endColumn": 34}, {"ruleId": "401", "severity": 1, "message": "414", "line": 23, "column": 23, "nodeType": "415", "endLine": 23, "endColumn": 24}, {"ruleId": "394", "severity": 1, "message": "452", "line": 1, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 13}, {"ruleId": "394", "severity": 1, "message": "423", "line": 15, "column": 47, "nodeType": "396", "messageId": "397", "endLine": 15, "endColumn": 58}, {"ruleId": "394", "severity": 1, "message": "412", "line": 15, "column": 60, "nodeType": "396", "messageId": "397", "endLine": 15, "endColumn": 67}, {"ruleId": "394", "severity": 1, "message": "413", "line": 15, "column": 69, "nodeType": "396", "messageId": "397", "endLine": 15, "endColumn": 74}, {"ruleId": "394", "severity": 1, "message": "453", "line": 16, "column": 19, "nodeType": "396", "messageId": "397", "endLine": 16, "endColumn": 22}, {"ruleId": "394", "severity": 1, "message": "454", "line": 1, "column": 8, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 15}, {"ruleId": "394", "severity": 1, "message": "455", "line": 2, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 20}, {"ruleId": "394", "severity": 1, "message": "456", "line": 2, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 32}, {"ruleId": "394", "severity": 1, "message": "457", "line": 2, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 2, "endColumn": 31}, {"ruleId": "394", "severity": 1, "message": "452", "line": 1, "column": 10, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 13}, {"ruleId": "394", "severity": 1, "message": "405", "line": 1, "column": 17, "nodeType": "396", "messageId": "397", "endLine": 1, "endColumn": 25}, {"ruleId": "458", "severity": 1, "message": "459", "line": 30, "column": 28, "nodeType": "460", "messageId": "461", "endLine": 30, "endColumn": 30}, {"ruleId": "458", "severity": 1, "message": "459", "line": 30, "column": 61, "nodeType": "460", "messageId": "461", "endLine": 30, "endColumn": 63}, "no-unused-vars", "'Header' is defined but never used.", "Identifier", "unusedVar", "'Footer' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "'response' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserBalance'. Either include it or remove the dependency array.", "ArrayExpression", ["462"], "'useState' is defined but never used.", "'Edit' is defined but never used.", "'Trash2' is defined but never used.", "'Search' is defined but never used.", "'Plus' is defined but never used.", "'filter' is defined but never used.", "'navigate' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "Assignments to the 'currentPage' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "Literal", "'TopUp' is defined but never used.", "'EditUserModal' is defined but never used.", "'selectedUser' is assigned a value but never used.", "'setSelectedUser' is assigned a value but never used.", "'showEdit' is assigned a value but never used.", "'setShowEdit' is assigned a value but never used.", "'totalElements' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'use' is defined but never used.", "'Deposit' is defined but never used.", "'Redeem' is defined but never used.", "'userProfile' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchBills', 'fetchTransaction', 'fetchUserBalance', and 'fetchUserById'. Either include them or remove the dependency array.", ["463"], "React Hook useEffect has a missing dependency: 'fetchTransaction'. Either include it or remove the dependency array.", ["464"], "'search' is assigned a value but never used.", "'setSearch' is assigned a value but never used.", "'showDeleteConfirm' is assigned a value but never used.", "'setShowDeleteConfirm' is assigned a value but never used.", "'customerToDelete' is assigned a value but never used.", "'setCustomerToDelete' is assigned a value but never used.", "'useEffect' is defined but never used.", "'UserApi' is defined but never used.", "'debounce' is defined but never used.", "React Hook useEffect has a missing dependency: 'onChangeSearch'. Either include it or remove the dependency array. If 'onChangeSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["465"], "'set' is defined but never used.", "'imageUrl' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'current' is defined but never used.", "'setSize' is assigned a value but never used.", "'deposits' is assigned a value but never used.", "'ChangePassword' is defined but never used.", "'get' is defined but never used.", "'res' is assigned a value but never used.", "'userApi' is defined but never used.", "'getBalance' is defined but never used.", "'getTransactionByUserID' is defined but never used.", "'getEmployeeByUsername' is defined but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", {"desc": "466", "fix": "467"}, {"desc": "468", "fix": "469"}, {"desc": "470", "fix": "471"}, {"desc": "472", "fix": "473"}, "Update the dependencies array to be: [fetchUserBalance]", {"range": "474", "text": "475"}, "Update the dependencies array to be: [balance, fetchBills, fetchTransaction, fetchUserBalance, fetchUserById]", {"range": "476", "text": "477"}, "Update the dependencies array to be: [params, startDate, endDate, fetchTransaction]", {"range": "478", "text": "479"}, "Update the dependencies array to be: [isReset, onChangeSearch]", {"range": "480", "text": "481"}, [3043, 3045], "[fetchUserBalance]", [4507, 4516], "[balance, fetchBills, fetchTransaction, fetchUserBalance, fetchUserById]", [4687, 4715], "[params, startDate, endDate, fetchTransaction]", [762, 771], "[isR<PERSON><PERSON>, onChangeSearch]"]