{"ast": null, "code": "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };", "map": {"version": 3, "names": ["hasW<PERSON>ow", "window", "getNodeName", "node", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "ownerDocument", "defaultView", "getDocumentElement", "_ref", "document", "documentElement", "value", "Node", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "isOverflowElement", "element", "overflow", "overflowX", "overflowY", "display", "getComputedStyle", "test", "includes", "isTableElement", "isTop<PERSON><PERSON>er", "some", "selector", "matches", "e", "isContainingBlock", "elementOrCss", "webkit", "isWebKit", "css", "containerType", "<PERSON><PERSON>ilter", "filter", "<PERSON><PERSON><PERSON><PERSON>", "contain", "getContainingBlock", "currentNode", "getParentNode", "isLastTraversableNode", "CSS", "supports", "getNodeScroll", "scrollLeft", "scrollTop", "scrollX", "scrollY", "result", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "body", "getOverflowAncestors", "list", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "frameElement", "getFrameElement", "concat", "visualViewport", "parent", "Object", "getPrototypeOf"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs"], "sourcesContent": ["function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n"], "mappings": "AAAA,SAASA,SAASA,CAAA,EAAG;EACnB,OAAO,OAAOC,MAAM,KAAK,WAAW;AACtC;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,MAAM,CAACD,IAAI,CAAC,EAAE;IAChB,OAAO,CAACA,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;EAC5C;EACA;EACA;EACA;EACA,OAAO,WAAW;AACpB;AACA,SAASC,SAASA,CAACJ,IAAI,EAAE;EACvB,IAAIK,mBAAmB;EACvB,OAAO,CAACL,IAAI,IAAI,IAAI,IAAI,CAACK,mBAAmB,GAAGL,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,mBAAmB,CAACE,WAAW,KAAKT,MAAM;AAClI;AACA,SAASU,kBAAkBA,CAACR,IAAI,EAAE;EAChC,IAAIS,IAAI;EACR,OAAO,CAACA,IAAI,GAAG,CAACR,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACU,QAAQ,KAAKZ,MAAM,CAACY,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,IAAI,CAACE,eAAe;AAChI;AACA,SAASV,MAAMA,CAACW,KAAK,EAAE;EACrB,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYC,IAAI,IAAID,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACC,IAAI;AACxE;AACA,SAASC,SAASA,CAACF,KAAK,EAAE;EACxB,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYG,OAAO,IAAIH,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACG,OAAO;AAC9E;AACA,SAASC,aAAaA,CAACJ,KAAK,EAAE;EAC5B,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EACA,OAAOe,KAAK,YAAYK,WAAW,IAAIL,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACK,WAAW;AACtF;AACA,SAASC,YAAYA,CAACN,KAAK,EAAE;EAC3B,IAAI,CAACf,SAAS,CAAC,CAAC,IAAI,OAAOsB,UAAU,KAAK,WAAW,EAAE;IACrD,OAAO,KAAK;EACd;EACA,OAAOP,KAAK,YAAYO,UAAU,IAAIP,KAAK,YAAYR,SAAS,CAACQ,KAAK,CAAC,CAACO,UAAU;AACpF;AACA,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EAClC,MAAM;IACJC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGC,gBAAgB,CAACL,OAAO,CAAC;EAC7B,OAAO,iCAAiC,CAACM,IAAI,CAACL,QAAQ,GAAGE,SAAS,GAAGD,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACK,QAAQ,CAACH,OAAO,CAAC;AAC9H;AACA,SAASI,cAAcA,CAACR,OAAO,EAAE;EAC/B,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAACO,QAAQ,CAAC7B,WAAW,CAACsB,OAAO,CAAC,CAAC;AAC7D;AACA,SAASS,UAAUA,CAACT,OAAO,EAAE;EAC3B,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC,CAACU,IAAI,CAACC,QAAQ,IAAI;IAClD,IAAI;MACF,OAAOX,OAAO,CAACY,OAAO,CAACD,QAAQ,CAAC;IAClC,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC,CAAC;AACJ;AACA,SAASC,iBAAiBA,CAACC,YAAY,EAAE;EACvC,MAAMC,MAAM,GAAGC,QAAQ,CAAC,CAAC;EACzB,MAAMC,GAAG,GAAGzB,SAAS,CAACsB,YAAY,CAAC,GAAGV,gBAAgB,CAACU,YAAY,CAAC,GAAGA,YAAY;;EAEnF;EACA;EACA,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAACL,IAAI,CAACnB,KAAK,IAAI2B,GAAG,CAAC3B,KAAK,CAAC,GAAG2B,GAAG,CAAC3B,KAAK,CAAC,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK2B,GAAG,CAACC,aAAa,GAAGD,GAAG,CAACC,aAAa,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,CAACH,MAAM,KAAKE,GAAG,CAACE,cAAc,GAAGF,GAAG,CAACE,cAAc,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAACJ,MAAM,KAAKE,GAAG,CAACG,MAAM,GAAGH,GAAG,CAACG,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,CAAC,CAACX,IAAI,CAACnB,KAAK,IAAI,CAAC2B,GAAG,CAACI,UAAU,IAAI,EAAE,EAAEf,QAAQ,CAAChB,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAACmB,IAAI,CAACnB,KAAK,IAAI,CAAC2B,GAAG,CAACK,OAAO,IAAI,EAAE,EAAEhB,QAAQ,CAAChB,KAAK,CAAC,CAAC;AACpiB;AACA,SAASiC,kBAAkBA,CAACxB,OAAO,EAAE;EACnC,IAAIyB,WAAW,GAAGC,aAAa,CAAC1B,OAAO,CAAC;EACxC,OAAOL,aAAa,CAAC8B,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAACF,WAAW,CAAC,EAAE;IACxE,IAAIX,iBAAiB,CAACW,WAAW,CAAC,EAAE;MAClC,OAAOA,WAAW;IACpB,CAAC,MAAM,IAAIhB,UAAU,CAACgB,WAAW,CAAC,EAAE;MAClC,OAAO,IAAI;IACb;IACAA,WAAW,GAAGC,aAAa,CAACD,WAAW,CAAC;EAC1C;EACA,OAAO,IAAI;AACb;AACA,SAASR,QAAQA,CAAA,EAAG;EAClB,IAAI,OAAOW,GAAG,KAAK,WAAW,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE,OAAO,KAAK;EAC7D,OAAOD,GAAG,CAACC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AACxD;AACA,SAASF,qBAAqBA,CAAChD,IAAI,EAAE;EACnC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC4B,QAAQ,CAAC7B,WAAW,CAACC,IAAI,CAAC,CAAC;AAClE;AACA,SAAS0B,gBAAgBA,CAACL,OAAO,EAAE;EACjC,OAAOjB,SAAS,CAACiB,OAAO,CAAC,CAACK,gBAAgB,CAACL,OAAO,CAAC;AACrD;AACA,SAAS8B,aAAaA,CAAC9B,OAAO,EAAE;EAC9B,IAAIP,SAAS,CAACO,OAAO,CAAC,EAAE;IACtB,OAAO;MACL+B,UAAU,EAAE/B,OAAO,CAAC+B,UAAU;MAC9BC,SAAS,EAAEhC,OAAO,CAACgC;IACrB,CAAC;EACH;EACA,OAAO;IACLD,UAAU,EAAE/B,OAAO,CAACiC,OAAO;IAC3BD,SAAS,EAAEhC,OAAO,CAACkC;EACrB,CAAC;AACH;AACA,SAASR,aAAaA,CAAC/C,IAAI,EAAE;EAC3B,IAAID,WAAW,CAACC,IAAI,CAAC,KAAK,MAAM,EAAE;IAChC,OAAOA,IAAI;EACb;EACA,MAAMwD,MAAM;EACZ;EACAxD,IAAI,CAACyD,YAAY;EACjB;EACAzD,IAAI,CAAC0D,UAAU;EACf;EACAxC,YAAY,CAAClB,IAAI,CAAC,IAAIA,IAAI,CAAC2D,IAAI;EAC/B;EACAnD,kBAAkB,CAACR,IAAI,CAAC;EACxB,OAAOkB,YAAY,CAACsC,MAAM,CAAC,GAAGA,MAAM,CAACG,IAAI,GAAGH,MAAM;AACpD;AACA,SAASI,0BAA0BA,CAAC5D,IAAI,EAAE;EACxC,MAAM0D,UAAU,GAAGX,aAAa,CAAC/C,IAAI,CAAC;EACtC,IAAIgD,qBAAqB,CAACU,UAAU,CAAC,EAAE;IACrC,OAAO1D,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACM,aAAa,CAACuD,IAAI,GAAG7D,IAAI,CAAC6D,IAAI;EACjE;EACA,IAAI7C,aAAa,CAAC0C,UAAU,CAAC,IAAItC,iBAAiB,CAACsC,UAAU,CAAC,EAAE;IAC9D,OAAOA,UAAU;EACnB;EACA,OAAOE,0BAA0B,CAACF,UAAU,CAAC;AAC/C;AACA,SAASI,oBAAoBA,CAAC9D,IAAI,EAAE+D,IAAI,EAAEC,eAAe,EAAE;EACzD,IAAIC,oBAAoB;EACxB,IAAIF,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,EAAE;EACX;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,IAAI;EACxB;EACA,MAAME,kBAAkB,GAAGN,0BAA0B,CAAC5D,IAAI,CAAC;EAC3D,MAAMmE,MAAM,GAAGD,kBAAkB,MAAM,CAACD,oBAAoB,GAAGjE,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2D,oBAAoB,CAACJ,IAAI,CAAC;EAChI,MAAMO,GAAG,GAAGhE,SAAS,CAAC8D,kBAAkB,CAAC;EACzC,IAAIC,MAAM,EAAE;IACV,MAAME,YAAY,GAAGC,eAAe,CAACF,GAAG,CAAC;IACzC,OAAOL,IAAI,CAACQ,MAAM,CAACH,GAAG,EAAEA,GAAG,CAACI,cAAc,IAAI,EAAE,EAAEpD,iBAAiB,CAAC8C,kBAAkB,CAAC,GAAGA,kBAAkB,GAAG,EAAE,EAAEG,YAAY,IAAIL,eAAe,GAAGF,oBAAoB,CAACO,YAAY,CAAC,GAAG,EAAE,CAAC;EAC/L;EACA,OAAON,IAAI,CAACQ,MAAM,CAACL,kBAAkB,EAAEJ,oBAAoB,CAACI,kBAAkB,EAAE,EAAE,EAAEF,eAAe,CAAC,CAAC;AACvG;AACA,SAASM,eAAeA,CAACF,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACK,MAAM,IAAIC,MAAM,CAACC,cAAc,CAACP,GAAG,CAACK,MAAM,CAAC,GAAGL,GAAG,CAACC,YAAY,GAAG,IAAI;AAClF;AAEA,SAAS3C,gBAAgB,EAAEmB,kBAAkB,EAAErC,kBAAkB,EAAE8D,eAAe,EAAEV,0BAA0B,EAAE7D,WAAW,EAAEoD,aAAa,EAAEW,oBAAoB,EAAEf,aAAa,EAAE3C,SAAS,EAAE+B,iBAAiB,EAAErB,SAAS,EAAEE,aAAa,EAAEgC,qBAAqB,EAAE/C,MAAM,EAAEmB,iBAAiB,EAAEF,YAAY,EAAEW,cAAc,EAAEC,UAAU,EAAEQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}