{"ast": null, "code": "import { millisecondsInSecond } from \"./constants.js\";\n\n/**\n * @name millisecondsToSeconds\n * @category Conversion Helpers\n * @summary Convert milliseconds to seconds.\n *\n * @description\n * Convert a number of milliseconds to a full number of seconds.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in seconds\n *\n * @example\n * // Convert 1000 milliseconds to seconds:\n * const result = millisecondsToSeconds(1000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToSeconds(1999)\n * //=> 1\n */\nexport function millisecondsToSeconds(milliseconds) {\n  const seconds = milliseconds / millisecondsInSecond;\n  return Math.trunc(seconds);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToSeconds;", "map": {"version": 3, "names": ["millisecondsInSecond", "millisecondsToSeconds", "milliseconds", "seconds", "Math", "trunc"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/millisecondsToSeconds.js"], "sourcesContent": ["import { millisecondsInSecond } from \"./constants.js\";\n\n/**\n * @name millisecondsToSeconds\n * @category Conversion Helpers\n * @summary Convert milliseconds to seconds.\n *\n * @description\n * Convert a number of milliseconds to a full number of seconds.\n *\n * @param milliseconds - The number of milliseconds to be converted\n *\n * @returns The number of milliseconds converted in seconds\n *\n * @example\n * // Convert 1000 milliseconds to seconds:\n * const result = millisecondsToSeconds(1000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToSeconds(1999)\n * //=> 1\n */\nexport function millisecondsToSeconds(milliseconds) {\n  const seconds = milliseconds / millisecondsInSecond;\n  return Math.trunc(seconds);\n}\n\n// Fallback for modularized imports:\nexport default millisecondsToSeconds;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,gBAAgB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,YAAY,EAAE;EAClD,MAAMC,OAAO,GAAGD,YAAY,GAAGF,oBAAoB;EACnD,OAAOI,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;AAC5B;;AAEA;AACA,eAAeF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}