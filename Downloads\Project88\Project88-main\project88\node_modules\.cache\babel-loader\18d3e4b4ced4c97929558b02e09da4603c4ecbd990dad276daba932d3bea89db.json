{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDecade} function options.\n */\n\n/**\n * @name getDecade\n * @category Decade Helpers\n * @summary Get the decade of the given date.\n *\n * @description\n * Get the decade of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The year of decade\n *\n * @example\n * // Which decade belongs 27 November 1942?\n * const result = getDecade(new Date(1942, 10, 27))\n * //=> 1940\n */\nexport function getDecade(date, options) {\n  // TODO: Switch to more technical definition in of decades that start with 1\n  // end with 0. I.e. 2001-2010 instead of current 2000-2009. It's a breaking\n  // change, so it can only be done in 4.0.\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = Math.floor(year / 10) * 10;\n  return decade;\n}\n\n// Fallback for modularized imports:\nexport default getDecade;", "map": {"version": 3, "names": ["toDate", "getDecade", "date", "options", "_date", "in", "year", "getFullYear", "decade", "Math", "floor"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/getDecade.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDecade} function options.\n */\n\n/**\n * @name getDecade\n * @category Decade Helpers\n * @summary Get the decade of the given date.\n *\n * @description\n * Get the decade of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The year of decade\n *\n * @example\n * // Which decade belongs 27 November 1942?\n * const result = getDecade(new Date(1942, 10, 27))\n * //=> 1940\n */\nexport function getDecade(date, options) {\n  // TODO: Switch to more technical definition in of decades that start with 1\n  // end with 0. I.e. 2001-2010 instead of current 2000-2009. It's a breaking\n  // change, so it can only be done in 4.0.\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const decade = Math.floor(year / 10) * 10;\n  return decade;\n}\n\n// Fallback for modularized imports:\nexport default getDecade;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC;EACA;EACA;EACA,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChC,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE;EACzC,OAAOE,MAAM;AACf;;AAEA;AACA,eAAeP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}