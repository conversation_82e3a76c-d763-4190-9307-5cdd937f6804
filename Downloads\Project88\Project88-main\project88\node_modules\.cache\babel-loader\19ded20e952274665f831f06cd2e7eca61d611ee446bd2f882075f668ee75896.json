{"ast": null, "code": "'use strict';\n\nvar NATIVE_BIND = require('../internals/function-bind-native');\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-function-prototype-bind, es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});", "map": {"version": 3, "names": ["NATIVE_BIND", "require", "FunctionPrototype", "Function", "prototype", "apply", "call", "module", "exports", "Reflect", "bind", "arguments"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/core-js-pure/internals/function-apply.js"], "sourcesContent": ["'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-function-prototype-bind, es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAE9D,IAAIC,iBAAiB,GAAGC,QAAQ,CAACC,SAAS;AAC1C,IAAIC,KAAK,GAAGH,iBAAiB,CAACG,KAAK;AACnC,IAAIC,IAAI,GAAGJ,iBAAiB,CAACI,IAAI;;AAEjC;AACAC,MAAM,CAACC,OAAO,GAAG,OAAOC,OAAO,IAAI,QAAQ,IAAIA,OAAO,CAACJ,KAAK,KAAKL,WAAW,GAAGM,IAAI,CAACI,IAAI,CAACL,KAAK,CAAC,GAAG,YAAY;EAC5G,OAAOC,IAAI,CAACD,KAAK,CAACA,KAAK,EAAEM,SAAS,CAAC;AACrC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}