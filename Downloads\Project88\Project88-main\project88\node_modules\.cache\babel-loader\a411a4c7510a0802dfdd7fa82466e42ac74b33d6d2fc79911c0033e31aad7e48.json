{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 15V5a2 2 0 0 0-2-2H9\",\n  key: \"43el77\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M3.6 3.6c-.4.3-.6.8-.6 1.4v16l4-4h10\",\n  key: \"pwpm4a\"\n}]];\nconst MessageSquareOff = createLucideIcon(\"message-square-off\", __iconNode);\nexport { __iconNode, MessageSquareOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageSquareOff", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\lucide-react\\src\\icons\\message-square-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15V5a2 2 0 0 0-2-2H9', key: '43el77' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M3.6 3.6c-.4.3-.6.8-.6 1.4v16l4-4h10', key: 'pwpm4a' }],\n];\n\n/**\n * @component @name MessageSquareOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVWNWEyIDIgMCAwIDAtMi0ySDkiIC8+CiAgPHBhdGggZD0ibTIgMiAyMCAyMCIgLz4KICA8cGF0aCBkPSJNMy42IDMuNmMtLjQuMy0uNi44LS42IDEuNHYxNmw0LTRoMTAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-square-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareOff = createLucideIcon('message-square-off', __iconNode);\n\nexport default MessageSquareOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACvE;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,uBAAsBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}