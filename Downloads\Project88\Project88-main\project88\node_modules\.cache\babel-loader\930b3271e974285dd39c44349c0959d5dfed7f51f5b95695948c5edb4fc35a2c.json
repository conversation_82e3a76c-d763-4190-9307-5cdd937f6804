{"ast": null, "code": "/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"1nb95v\"\n}], [\"path\", {\n  d: \"M12 6h.01\",\n  key: \"1vi96p\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"14\",\n  r: \"4\",\n  key: \"1jruaj\"\n}], [\"path\", {\n  d: \"M12 14h.01\",\n  key: \"1etili\"\n}]];\nconst Speaker = createLucideIcon(\"speaker\", __iconNode);\nexport { __iconNode, Speaker as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "cx", "cy", "r", "Speaker", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\Project88\\Project88-main\\project88\\node_modules\\lucide-react\\src\\icons\\speaker.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', key: '1nb95v' }],\n  ['path', { d: 'M12 6h.01', key: '1vi96p' }],\n  ['circle', { cx: '12', cy: '14', r: '4', key: '1jruaj' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n];\n\n/**\n * @component @name Speaker\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xMiA2aC4wMSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE0IiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xMiAxNGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/speaker\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Speaker = createLucideIcon('speaker', __iconNode);\n\nexport default Speaker;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAU;EAAEE,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC7C;AAaM,MAAAK,OAAA,GAAUC,gBAAiB,YAAWZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}