{"ast": null, "code": "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfWeekYear } from \"./startOfWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setWeekYear} function options.\n */\n\n/**\n * @name setWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Set the local week-numbering year to the given date.\n *\n * @description\n * Set the local week-numbering year to the given date,\n * saving the week number and the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param weekYear - The local week-numbering year of the new date\n * @param options - An object with options\n *\n * @returns The new date with the local week-numbering year set\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010 with default options:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004)\n * //=> Sat Jan 03 2004 00:00:00\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010,\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport function setWeekYear(date, weekYear, options) {\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate = options?.firstWeekContainsDate ?? options?.locale?.options?.firstWeekContainsDate ?? defaultOptions.firstWeekContainsDate ?? defaultOptions.locale?.options?.firstWeekContainsDate ?? 1;\n  const diff = differenceInCalendarDays(toDate(date, options?.in), startOfWeekYear(date, options), options);\n  const firstWeek = constructFrom(options?.in || date, 0);\n  firstWeek.setFullYear(weekYear, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  const date_ = startOfWeekYear(firstWeek, options);\n  date_.setDate(date_.getDate() + diff);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setWeekYear;", "map": {"version": 3, "names": ["getDefaultOptions", "constructFrom", "differenceInCalendarDays", "startOfWeekYear", "toDate", "setWeekYear", "date", "weekYear", "options", "defaultOptions", "firstWeekContainsDate", "locale", "diff", "in", "firstWeek", "setFullYear", "setHours", "date_", "setDate", "getDate"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/setWeekYear.js"], "sourcesContent": ["import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfWeekYear } from \"./startOfWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setWeekYear} function options.\n */\n\n/**\n * @name setWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Set the local week-numbering year to the given date.\n *\n * @description\n * Set the local week-numbering year to the given date,\n * saving the week number and the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param weekYear - The local week-numbering year of the new date\n * @param options - An object with options\n *\n * @returns The new date with the local week-numbering year set\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010 with default options:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004)\n * //=> Sat Jan 03 2004 00:00:00\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010,\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport function setWeekYear(date, weekYear, options) {\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const diff = differenceInCalendarDays(\n    toDate(date, options?.in),\n    startOfWeekYear(date, options),\n    options,\n  );\n\n  const firstWeek = constructFrom(options?.in || date, 0);\n  firstWeek.setFullYear(weekYear, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n\n  const date_ = startOfWeekYear(firstWeek, options);\n  date_.setDate(date_.getDate() + diff);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setWeekYear;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACnD,MAAMC,cAAc,GAAGT,iBAAiB,CAAC,CAAC;EAC1C,MAAMU,qBAAqB,GACzBF,OAAO,EAAEE,qBAAqB,IAC9BF,OAAO,EAAEG,MAAM,EAAEH,OAAO,EAAEE,qBAAqB,IAC/CD,cAAc,CAACC,qBAAqB,IACpCD,cAAc,CAACE,MAAM,EAAEH,OAAO,EAAEE,qBAAqB,IACrD,CAAC;EAEH,MAAME,IAAI,GAAGV,wBAAwB,CACnCE,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEK,EAAE,CAAC,EACzBV,eAAe,CAACG,IAAI,EAAEE,OAAO,CAAC,EAC9BA,OACF,CAAC;EAED,MAAMM,SAAS,GAAGb,aAAa,CAACO,OAAO,EAAEK,EAAE,IAAIP,IAAI,EAAE,CAAC,CAAC;EACvDQ,SAAS,CAACC,WAAW,CAACR,QAAQ,EAAE,CAAC,EAAEG,qBAAqB,CAAC;EACzDI,SAAS,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAE9B,MAAMC,KAAK,GAAGd,eAAe,CAACW,SAAS,EAAEN,OAAO,CAAC;EACjDS,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGP,IAAI,CAAC;EACrC,OAAOK,KAAK;AACd;;AAEA;AACA,eAAeZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}