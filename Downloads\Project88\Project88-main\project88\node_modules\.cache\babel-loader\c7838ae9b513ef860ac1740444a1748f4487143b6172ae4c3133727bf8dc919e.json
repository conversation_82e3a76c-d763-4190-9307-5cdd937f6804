{"ast": null, "code": "import { addDays } from \"./addDays.js\";\nimport { getISODay } from \"./getISODay.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISODay} function options.\n */\n\n/**\n * @name setISODay\n * @category Weekday Helpers\n * @summary Set the day of the ISO week to the given date.\n *\n * @description\n * Set the day of the ISO week to the given date.\n * ISO week starts with Monday.\n * 7 is the index of Sunday, 1 is the index of Monday, etc.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the day of the ISO week set\n *\n * @example\n * // Set Sunday to 1 September 2014:\n * const result = setISODay(new Date(2014, 8, 1), 7)\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setISODay(date, day, options) {\n  const date_ = toDate(date, options?.in);\n  const currentDay = getISODay(date_, options);\n  const diff = day - currentDay;\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setISODay;", "map": {"version": 3, "names": ["addDays", "getISODay", "toDate", "setISODay", "date", "day", "options", "date_", "in", "currentDay", "diff"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/setISODay.js"], "sourcesContent": ["import { addDays } from \"./addDays.js\";\nimport { getISODay } from \"./getISODay.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISODay} function options.\n */\n\n/**\n * @name setISODay\n * @category Weekday Helpers\n * @summary Set the day of the ISO week to the given date.\n *\n * @description\n * Set the day of the ISO week to the given date.\n * ISO week starts with Monday.\n * 7 is the index of Sunday, 1 is the index of Monday, etc.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the day of the ISO week set\n *\n * @example\n * // Set Sunday to 1 September 2014:\n * const result = setISODay(new Date(2014, 8, 1), 7)\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setISODay(date, day, options) {\n  const date_ = toDate(date, options?.in);\n  const currentDay = getISODay(date_, options);\n  const diff = day - currentDay;\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setISODay;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC5C,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,UAAU,GAAGR,SAAS,CAACM,KAAK,EAAED,OAAO,CAAC;EAC5C,MAAMI,IAAI,GAAGL,GAAG,GAAGI,UAAU;EAC7B,OAAOT,OAAO,CAACO,KAAK,EAAEG,IAAI,EAAEJ,OAAO,CAAC;AACtC;;AAEA;AACA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}