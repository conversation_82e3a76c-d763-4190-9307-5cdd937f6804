{"ast": null, "code": "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { getDate } from \"./getDate.js\";\nimport { getDay } from \"./getDay.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekOfMonth} function options.\n */\n\n/**\n * @name getWeekOfMonth\n * @category Week Helpers\n * @summary Get the week of the month of the given date.\n *\n * @description\n * Get the week of the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The week of month\n *\n * @example\n * // Which week of the month is 9 November 2017?\n * const result = getWeekOfMonth(new Date(2017, 10, 9))\n * //=> 2\n */\nexport function getWeekOfMonth(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn = options?.weekStartsOn ?? options?.locale?.options?.weekStartsOn ?? defaultOptions.weekStartsOn ?? defaultOptions.locale?.options?.weekStartsOn ?? 0;\n  const currentDayOfMonth = getDate(toDate(date, options?.in));\n  if (isNaN(currentDayOfMonth)) return NaN;\n  const startWeekDay = getDay(startOfMonth(date, options));\n  let lastDayOfFirstWeek = weekStartsOn - startWeekDay;\n  if (lastDayOfFirstWeek <= 0) lastDayOfFirstWeek += 7;\n  const remainingDaysAfterFirstWeek = currentDayOfMonth - lastDayOfFirstWeek;\n  return Math.ceil(remainingDaysAfterFirstWeek / 7) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeekOfMonth;", "map": {"version": 3, "names": ["getDefaultOptions", "getDate", "getDay", "startOfMonth", "toDate", "getWeekOfMonth", "date", "options", "defaultOptions", "weekStartsOn", "locale", "currentDayOfMonth", "in", "isNaN", "NaN", "startWeekDay", "lastDayOfFirstWeek", "remainingDaysAfterFirstWeek", "Math", "ceil"], "sources": ["C:/Users/<USER>/Downloads/Project88/Project88-main/project88/node_modules/date-fns/getWeekOfMonth.js"], "sourcesContent": ["import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { getDate } from \"./getDate.js\";\nimport { getDay } from \"./getDay.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekOfMonth} function options.\n */\n\n/**\n * @name getWeekOfMonth\n * @category Week Helpers\n * @summary Get the week of the month of the given date.\n *\n * @description\n * Get the week of the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The week of month\n *\n * @example\n * // Which week of the month is 9 November 2017?\n * const result = getWeekOfMonth(new Date(2017, 10, 9))\n * //=> 2\n */\nexport function getWeekOfMonth(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const currentDayOfMonth = getDate(toDate(date, options?.in));\n  if (isNaN(currentDayOfMonth)) return NaN;\n\n  const startWeekDay = getDay(startOfMonth(date, options));\n\n  let lastDayOfFirstWeek = weekStartsOn - startWeekDay;\n  if (lastDayOfFirstWeek <= 0) lastDayOfFirstWeek += 7;\n\n  const remainingDaysAfterFirstWeek = currentDayOfMonth - lastDayOfFirstWeek;\n  return Math.ceil(remainingDaysAfterFirstWeek / 7) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeekOfMonth;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC5C,MAAMC,cAAc,GAAGR,iBAAiB,CAAC,CAAC;EAC1C,MAAMS,YAAY,GAChBF,OAAO,EAAEE,YAAY,IACrBF,OAAO,EAAEG,MAAM,EAAEH,OAAO,EAAEE,YAAY,IACtCD,cAAc,CAACC,YAAY,IAC3BD,cAAc,CAACE,MAAM,EAAEH,OAAO,EAAEE,YAAY,IAC5C,CAAC;EAEH,MAAME,iBAAiB,GAAGV,OAAO,CAACG,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEK,EAAE,CAAC,CAAC;EAC5D,IAAIC,KAAK,CAACF,iBAAiB,CAAC,EAAE,OAAOG,GAAG;EAExC,MAAMC,YAAY,GAAGb,MAAM,CAACC,YAAY,CAACG,IAAI,EAAEC,OAAO,CAAC,CAAC;EAExD,IAAIS,kBAAkB,GAAGP,YAAY,GAAGM,YAAY;EACpD,IAAIC,kBAAkB,IAAI,CAAC,EAAEA,kBAAkB,IAAI,CAAC;EAEpD,MAAMC,2BAA2B,GAAGN,iBAAiB,GAAGK,kBAAkB;EAC1E,OAAOE,IAAI,CAACC,IAAI,CAACF,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;AACvD;;AAEA;AACA,eAAeZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}